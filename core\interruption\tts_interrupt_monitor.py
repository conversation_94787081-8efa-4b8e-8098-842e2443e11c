"""
TTS Interrupt Monitoring System

This module provides real-time TTS playback monitoring with interrupt detection
and handling capabilities for the voice agent platform.
"""

import asyncio
import threading
import time
import numpy as np
from typing import Dict, Any, Optional, Callable, AsyncContextManager
from datetime import datetime
import contextlib
import weakref
from collections import deque
import concurrent.futures

try:
    import pygame
    PYGAME_AVAILABLE = True
except ImportError:
    PYGAME_AVAILABLE = False

try:
    import sounddevice as sd
    SOUNDDEVICE_AVAILABLE = True
except ImportError:
    SOUNDDEVICE_AVAILABLE = False

from core.logging.logger_config import get_module_logger
from schemas.outputSchema import StateOutput, StatusType, StatusCode
from core.state_manager.state_output import InterruptState
from core.config.interrupt_config import get_interrupt_config
from core.interruption.interrupt_manager import save_interrupt_audio


class CircularAudioBuffer:
    """Efficient circular buffer for audio chunk management."""

    def __init__(self, max_size: int = 100):
        self.buffer = deque(maxlen=max_size)
        self.max_size = max_size
        self._lock = asyncio.Lock()

    async def add_chunk(self, audio_data: bytes, timestamp: float = None):
        """Add audio chunk to buffer."""
        async with self._lock:
            if timestamp is None:
                timestamp = asyncio.get_event_loop().time()
            self.buffer.append({'data': audio_data, 'timestamp': timestamp})

    async def get_recent_chunks(self, count: int = 5) -> list:
        """Get recent audio chunks."""
        async with self._lock:
            return list(self.buffer)[-count:] if len(self.buffer) >= count else list(self.buffer)

    async def clear(self):
        """Clear the buffer."""
        async with self._lock:
            self.buffer.clear()


class AudioDevicePool:
    """Resource pool for audio devices with conflict detection."""

    def __init__(self, max_devices: int = 3):
        self.max_devices = max_devices
        self.available_devices = asyncio.Queue(maxsize=max_devices)
        self.in_use_devices = set()
        self._lock = asyncio.Lock()
        self.logger = get_module_logger("AudioDevicePool")

        # Initialize device pool
        asyncio.create_task(self._initialize_pool())

    async def _initialize_pool(self):
        """Initialize the device pool."""
        try:
            if SOUNDDEVICE_AVAILABLE:
                devices = sd.query_devices()
                input_devices = [i for i, d in enumerate(devices) if d['max_input_channels'] > 0]

                # Add available input devices to pool
                for device_id in input_devices[:self.max_devices]:
                    await self.available_devices.put(device_id)

                self.logger.info(f"Initialized audio device pool with {len(input_devices)} devices")
            else:
                # Fallback: use default device
                await self.available_devices.put(None)
                self.logger.warning("SoundDevice not available, using fallback device")

        except Exception as e:
            self.logger.error(f"Failed to initialize audio device pool: {e}")
            # Ensure at least one device in pool
            await self.available_devices.put(None)

    @contextlib.asynccontextmanager
    async def acquire_device(self, timeout: float = 5.0):
        """Acquire an audio device with timeout and automatic cleanup."""
        device_id = None
        try:
            # Wait for available device with timeout
            device_id = await asyncio.wait_for(
                self.available_devices.get(),
                timeout=timeout
            )

            async with self._lock:
                self.in_use_devices.add(device_id)

            self.logger.debug(f"Acquired audio device: {device_id}")
            yield device_id

        except asyncio.TimeoutError:
            self.logger.error("Timeout waiting for audio device")
            raise RuntimeError("No audio devices available")
        except Exception as e:
            self.logger.error(f"Error acquiring audio device: {e}")
            raise
        finally:
            if device_id is not None:
                async with self._lock:
                    self.in_use_devices.discard(device_id)
                await self.available_devices.put(device_id)
                self.logger.debug(f"Released audio device: {device_id}")

    async def detect_conflicts(self) -> list:
        """Detect potential device conflicts."""
        async with self._lock:
            conflicts = []
            if len(self.in_use_devices) > 1:
                conflicts.append("Multiple devices in use simultaneously")
            return conflicts


class AsyncAudioProcessor:
    """Async audio processing with exponential backoff and error recovery."""

    def __init__(self, device_pool: AudioDevicePool, interrupt_config=None):
        self.device_pool = device_pool
        self.interrupt_config = interrupt_config
        self.logger = get_module_logger("AsyncAudioProcessor")
        self.executor = concurrent.futures.ThreadPoolExecutor(max_workers=2)

        # Exponential backoff parameters
        self.base_delay = 0.1
        self.max_delay = 5.0
        self.backoff_factor = 2.0
        self.max_retries = 3

    async def _exponential_backoff(self, attempt: int) -> float:
        """Calculate exponential backoff delay."""
        delay = min(self.base_delay * (self.backoff_factor ** attempt), self.max_delay)
        await asyncio.sleep(delay)
        return delay

    async def capture_audio_async(self, duration: float, sample_rate: int = 16000,
                                 channels: int = 1, device_id: Optional[int] = None) -> bytes:
        """Capture audio asynchronously with error recovery."""
        for attempt in range(self.max_retries):
            try:
                # Run audio capture in thread pool to avoid blocking
                loop = asyncio.get_event_loop()
                audio_data = await loop.run_in_executor(
                    self.executor,
                    self._capture_audio_sync,
                    duration, sample_rate, channels, device_id
                )
                return audio_data

            except Exception as e:
                self.logger.warning(f"Audio capture attempt {attempt + 1} failed: {e}")
                if attempt < self.max_retries - 1:
                    await self._exponential_backoff(attempt)
                else:
                    raise RuntimeError(f"Audio capture failed after {self.max_retries} attempts: {e}")

    def _capture_audio_sync(self, duration: float, sample_rate: int,
                           channels: int, device_id: Optional[int]) -> bytes:
        """Synchronous audio capture for thread pool execution."""
        if not SOUNDDEVICE_AVAILABLE:
            raise RuntimeError("SoundDevice not available for audio capture")

        import sounddevice as sd
        audio = sd.rec(
            int(duration * sample_rate),
            samplerate=sample_rate,
            channels=channels,
            dtype='int16',
            device=device_id
        )
        sd.wait()
        return audio.tobytes()

    async def detect_voice_activity_async(self, audio_data: bytes, threshold: float = 0.05) -> dict:
        """Async VAD processing with error recovery."""
        for attempt in range(self.max_retries):
            try:
                # Run VAD in thread pool
                loop = asyncio.get_event_loop()
                result = await loop.run_in_executor(
                    self.executor,
                    self._vad_sync,
                    audio_data, threshold
                )
                return result

            except Exception as e:
                self.logger.warning(f"VAD attempt {attempt + 1} failed: {e}")
                if attempt < self.max_retries - 1:
                    await self._exponential_backoff(attempt)
                else:
                    return {'has_voice': False, 'error': str(e)}

    def _vad_sync(self, audio_data: bytes, threshold: float) -> dict:
        """Synchronous VAD processing for thread pool execution."""
        try:
            from utils.audio_utils import AudioProcessor
            ap = AudioProcessor(self.interrupt_config)
            result = ap.detect_voice_activity(audio_data, threshold=threshold)

            if result.status.value == 'success':
                return {
                    'has_voice': result.outputs.get('has_voice', False),
                    'energy': result.outputs.get('energy', 0),
                    'method': result.outputs.get('vad_method', 'unknown')
                }
            else:
                return {'has_voice': False, 'error': result.message}

        except Exception as e:
            return {'has_voice': False, 'error': str(e)}

    async def cleanup(self):
        """Cleanup async audio processor resources."""
        if hasattr(self, 'executor'):
            self.executor.shutdown(wait=True)
            self.logger.debug("Audio processor executor shutdown complete")


class TTSInterruptMonitor:
    """
    Enhanced TTS monitor with async audio processing and resource management.
    Provides pause/resume functionality and interrupt event handling.
    """

    def __init__(self, session_id: str, memory_manager, interrupt_config=None, agent_registry=None):
        self.session_id = session_id
        self.memory_manager = memory_manager
        self.interrupt_config = interrupt_config
        self.agent_registry = agent_registry
        self.logger = get_module_logger("TTSInterruptMonitor", session_id=session_id)

        # Async components
        self.device_pool = AudioDevicePool()
        self.audio_processor = AsyncAudioProcessor(self.device_pool, interrupt_config)
        self.audio_buffer = CircularAudioBuffer()

        # Async synchronization primitives
        self.interrupt_event = asyncio.Event()
        self.stop_monitoring = asyncio.Event()
        self.playback_lock = asyncio.Lock()

        # Playback state
        self.is_playing = False
        self.is_paused = False
        self.current_audio_path = None
        self.playback_start_time = None
        self.pause_time = None
        self.total_pause_duration = 0

        # Interrupt detection
        self.interrupt_detected = False
        self.monitoring_task = None

    @contextlib.asynccontextmanager
    async def audio_session(self):
        """Context manager for audio session with proper cleanup."""
        try:
            # Initialize pygame if available
            if PYGAME_AVAILABLE:
                try:
                    pygame.mixer.quit()  # Clean up any existing mixer
                    pygame.mixer.init(frequency=22050, size=-16, channels=2, buffer=512)
                    self.logger.debug("Pygame audio system initialized")
                except Exception as e:
                    self.logger.warning(f"Pygame initialization failed: {e}")

            yield self

        except Exception as e:
            self.logger.error(f"Error in audio session: {e}")
            raise
        finally:
            # Cleanup resources
            await self._cleanup_session()

    async def _cleanup_session(self):
        """Clean up audio session resources."""
        try:
            # Stop monitoring
            if self.monitoring_task and not self.monitoring_task.done():
                self.stop_monitoring.set()
                try:
                    await asyncio.wait_for(self.monitoring_task, timeout=3.0)
                except asyncio.TimeoutError:
                    self.logger.warning("Monitoring task cleanup timeout")
                    self.monitoring_task.cancel()

            # Stop audio playback
            if PYGAME_AVAILABLE and self.is_playing:
                try:
                    pygame.mixer.music.stop()
                    pygame.mixer.quit()
                except Exception as e:
                    self.logger.warning(f"Pygame cleanup error: {e}")

            # Clear audio buffer
            await self.audio_buffer.clear()

            # Cleanup audio processor
            await self.audio_processor.cleanup()

            # Reset state
            self.is_playing = False
            self.is_paused = False
            self.interrupt_detected = False
            self.interrupt_event.clear()
            self.stop_monitoring.clear()

            self.logger.debug("Audio session cleanup completed")

        except Exception as e:
            self.logger.error(f"Error during session cleanup: {e}")

    async def async_voice_monitoring(self, interrupt_config, state_manager=None):
        """Enhanced async voice monitoring with circular buffers and device pooling."""
        try:
            # Get monitoring parameters
            chunk_duration = 0.4  # Shorter for better responsiveness
            sample_rate = 16000
            channels = 1
            vad_threshold = (interrupt_config.global_settings.vad_threshold
                           if interrupt_config else 0.05)

            # Adaptive feedback detection
            baseline_rms = 0.0
            rms_history = deque(maxlen=10)
            feedback_confidence = 0.0

            self.logger.info("Starting enhanced async voice monitoring...")

            async with self.device_pool.acquire_device() as device_id:
                while not self.stop_monitoring.is_set():
                    try:
                        # Check if playback is still active
                        if PYGAME_AVAILABLE and not pygame.mixer.music.get_busy():
                            self.logger.debug("Playback finished, stopping monitoring")
                            break

                        # Skip if monitoring disabled
                        if (state_manager and
                            getattr(state_manager, 'skip_interrupt_monitoring', False)):
                            await asyncio.sleep(0.1)
                            continue

                        # Check configuration
                        if interrupt_config and not interrupt_config.global_settings.enabled:
                            await asyncio.sleep(0.1)
                            continue

                        # Capture audio asynchronously
                        audio_data = await self.audio_processor.capture_audio_async(
                            chunk_duration, sample_rate, channels, device_id
                        )

                        # Add to circular buffer
                        await self.audio_buffer.add_chunk(audio_data)

                        # Enhanced feedback detection
                        feedback_result = await self._analyze_audio_feedback(
                            audio_data, baseline_rms, rms_history, feedback_confidence
                        )

                        if feedback_result['is_feedback']:
                            feedback_confidence = min(feedback_confidence + 0.1, 1.0)
                            await asyncio.sleep(0.05)
                            continue
                        else:
                            feedback_confidence = max(feedback_confidence - 0.05, 0.0)

                        # Update baseline
                        rms_history.append(feedback_result['rms'])
                        if len(rms_history) >= 3:
                            baseline_rms = np.median(list(rms_history))

                        # VAD processing
                        vad_result = await self.audio_processor.detect_voice_activity_async(
                            audio_data, vad_threshold
                        )

                        if vad_result.get('has_voice', False):
                            energy = vad_result.get('energy', 0)
                            self.logger.info(f"🎤 Voice detected! Energy: {energy:.0f}")

                            # Enhanced confirmation
                            if await self._confirm_voice_activity(device_id, vad_threshold):
                                self.logger.info("🎯 Voice confirmed! Triggering interrupt...")
                                self.interrupt_event.set()
                                break

                        # Brief pause to prevent CPU overload
                        await asyncio.sleep(0.02)

                    except Exception as e:
                        self.logger.warning(f"Monitoring iteration error: {e}")
                        await asyncio.sleep(0.1)
                        continue

        except Exception as e:
            self.logger.error(f"Voice monitoring error: {e}")
        finally:
            self.logger.info("Async voice monitoring stopped")

    async def _analyze_audio_feedback(self, audio_data: bytes, baseline_rms: float,
                                    rms_history: deque, feedback_confidence: float) -> dict:
        """Analyze audio for TTS feedback with multiple criteria."""
        try:
            import numpy as np

            # Convert audio data
            audio_samples = np.frombuffer(audio_data, dtype=np.int16)
            if len(audio_samples) == 0:
                return {'is_feedback': False, 'rms': 0.0, 'reasons': []}

            # Calculate audio metrics
            audio_squared = audio_samples.astype(np.float64) ** 2
            mean_squared = np.mean(audio_squared)
            audio_rms = np.sqrt(max(mean_squared, 0.0))

            audio_peak = np.max(np.abs(audio_samples))
            peak_to_rms_ratio = audio_peak / (audio_rms + 1e-6)

            # Feedback detection criteria
            is_feedback = False
            reasons = []

            # Adaptive thresholds
            base_peak_threshold = 12.0 + (feedback_confidence * 3.0)

            # Criterion 1: Peak-to-RMS ratio
            if peak_to_rms_ratio > base_peak_threshold and audio_rms > 3000:
                is_feedback = True
                reasons.append(f"peak/RMS={peak_to_rms_ratio:.1f}")

            # Criterion 2: RMS spike above baseline
            if baseline_rms > 0 and audio_rms > (baseline_rms * 3.0) and audio_rms > 8000:
                is_feedback = True
                reasons.append(f"RMS spike={audio_rms:.0f}")

            # Criterion 3: Low variance (consistent amplitude)
            audio_variance = np.var(audio_samples.astype(np.float64))
            if audio_variance < (audio_rms * 0.1) and audio_rms > 5000:
                is_feedback = True
                reasons.append("low variance")

            return {
                'is_feedback': is_feedback,
                'rms': audio_rms,
                'peak_to_rms_ratio': peak_to_rms_ratio,
                'reasons': reasons
            }

        except Exception as e:
            self.logger.warning(f"Audio feedback analysis error: {e}")
            return {'is_feedback': False, 'rms': 0.0, 'reasons': []}

    async def _confirm_voice_activity(self, device_id: int, vad_threshold: float) -> bool:
        """Confirm voice activity with secondary check."""
        try:
            import os

            # Use simplified confirmation for testing
            use_simple = os.getenv('SIMPLE_INTERRUPT_CONFIRMATION', 'true').lower() == 'true'

            if use_simple:
                # Quick confirmation
                await asyncio.sleep(0.05)
                confirm_audio = await self.audio_processor.capture_audio_async(
                    0.15, 16000, 1, device_id
                )
                confirm_result = await self.audio_processor.detect_voice_activity_async(
                    confirm_audio, vad_threshold
                )
                return confirm_result.get('has_voice', False)
            else:
                # Complex confirmation
                await asyncio.sleep(0.1)
                confirm_audio = await self.audio_processor.capture_audio_async(
                    0.25, 16000, 1, device_id
                )
                confirm_result = await self.audio_processor.detect_voice_activity_async(
                    confirm_audio, vad_threshold
                )
                return confirm_result.get('has_voice', False)

        except Exception as e:
            self.logger.warning(f"Voice confirmation error: {e}")
            return False
        self.interrupt_callback = None
        self.monitoring_active = False
        
        # Threading for background monitoring
        self.monitor_thread = None
        self.stop_monitoring_event = threading.Event()

        # Initialize pygame if available
        if PYGAME_AVAILABLE:
            try:
                pygame.mixer.init()
                self.logger.info("Pygame mixer initialized for TTS playback")
            except Exception as e:
                self.logger.warning(f"Failed to initialize pygame mixer: {e}")
    
    async def start_tts_with_interrupt_monitoring(self, audio_path: str, 
                                                interrupt_callback: Optional[Callable] = None) -> StateOutput:
        """
        Start TTS playback with real-time interrupt monitoring.
        
        Args:
            audio_path: Path to the audio file to play
            interrupt_callback: Optional callback function for interrupt events
            
        Returns:
            StateOutput with monitoring status
        """
        try:
            self.logger.info(
                "Starting TTS with interrupt monitoring",
                action="start_tts_with_interrupt_monitoring",
                input_data={"audio_path": audio_path},
                layer="tts_interrupt_monitor"
            )
            
            # Reset state
            self.current_audio_path = audio_path
            self.interrupt_callback = interrupt_callback
            self.interrupt_detected = False
            self.is_playing = True
            self.is_paused = False
            self.playback_start_time = time.time()
            self.total_pause_duration = 0
            self.stop_monitoring_event.clear()
            
            # Store TTS playback state in memory
            await self.memory_manager.set_tts_playback_state(
                audio_path=audio_path,
                status="playing",
                playback_position=0.0
            )
            
            # Start background monitoring
            self.monitoring_active = True
            self.monitor_thread = threading.Thread(
                target=self._background_monitor_worker,
                daemon=True
            )
            self.monitor_thread.start()
            
            # Start audio playback if pygame is available
            if PYGAME_AVAILABLE:
                try:
                    pygame.mixer.music.load(audio_path)
                    pygame.mixer.music.play()
                    self.logger.info("Started pygame audio playback")
                except Exception as e:
                    self.logger.warning(f"Failed to start pygame playback: {e}")
            
            return StateOutput(
                status=StatusType.SUCCESS,
                message="TTS monitoring started successfully",
                code=StatusCode.OK,
                outputs={
                    "monitoring_active": True,
                    "audio_path": audio_path,
                    "playback_started": True
                },
                meta={"tts_monitor": "active"}
            )
            
        except Exception as e:
            self.logger.error(
                "Error starting TTS with interrupt monitoring",
                action="start_tts_with_interrupt_monitoring",
                reason=str(e),
                layer="tts_interrupt_monitor"
            )
            
            return StateOutput(
                status=StatusType.ERROR,
                message=f"Failed to start TTS monitoring: {str(e)}",
                code=StatusCode.INTERNAL_ERROR,
                outputs={},
                meta={"error": str(e)}
            )
    
    async def pause_tts_playback(self) -> StateOutput:
        """
        Pause the current TTS playback.
        
        Returns:
            StateOutput with pause status
        """
        try:
            if not self.is_playing or self.is_paused:
                return StateOutput(
                    status=StatusType.ERROR,
                    message="No active playback to pause",
                    code=StatusCode.BAD_REQUEST,
                    outputs={},
                    meta={"error": "no_active_playback"}
                )
            
            self.is_paused = True
            self.pause_time = time.time()
            
            # Pause pygame playback if available
            if PYGAME_AVAILABLE:
                try:
                    pygame.mixer.music.pause()
                    self.logger.info("Paused pygame audio playback")
                except Exception as e:
                    self.logger.warning(f"Failed to pause pygame playback: {e}")
            
            # Update memory state
            current_position = self.get_current_playback_position()
            await self.memory_manager.set_tts_playback_state(
                audio_path=self.current_audio_path,
                status="paused",
                playback_position=current_position
            )
            
            self.logger.info(
                "TTS playback paused",
                action="pause_tts_playback",
                output_data={"playback_position": current_position},
                layer="tts_interrupt_monitor"
            )
            
            return StateOutput(
                status=StatusType.SUCCESS,
                message="TTS playback paused",
                code=StatusCode.OK,
                outputs={
                    "paused": True,
                    "playback_position": current_position
                },
                meta={"playback_state": "paused"}
            )
            
        except Exception as e:
            self.logger.error(
                "Error pausing TTS playback",
                action="pause_tts_playback",
                reason=str(e),
                layer="tts_interrupt_monitor"
            )
            
            return StateOutput(
                status=StatusType.ERROR,
                message=f"Failed to pause TTS playback: {str(e)}",
                code=StatusCode.INTERNAL_ERROR,
                outputs={},
                meta={"error": str(e)}
            )
    
    async def resume_tts_playback(self, resume_from_position: Optional[float] = None) -> StateOutput:
        """
        Resume TTS playback from current or specified position.
        
        Args:
            resume_from_position: Optional position to resume from (in seconds)
            
        Returns:
            StateOutput with resume status
        """
        try:
            if not self.is_paused and resume_from_position is None:
                return StateOutput(
                    status=StatusType.ERROR,
                    message="No paused playback to resume",
                    code=StatusCode.BAD_REQUEST,
                    outputs={},
                    meta={"error": "no_paused_playback"}
                )
            
            # Calculate resume position
            if resume_from_position is not None:
                # Resume from specific position (for irreversible actions)
                self.playback_start_time = time.time() - resume_from_position
                self.total_pause_duration = 0
                resume_pos = resume_from_position
            else:
                # Resume from where we paused
                if self.pause_time:
                    self.total_pause_duration += time.time() - self.pause_time
                    self.pause_time = None
                resume_pos = self.get_current_playback_position()
            
            self.is_paused = False
            
            # Resume pygame playback if available
            if PYGAME_AVAILABLE:
                try:
                    if resume_from_position is not None:
                        # For specific position, reload and play from start
                        # (pygame doesn't support seeking, so this is a limitation)
                        pygame.mixer.music.load(self.current_audio_path)
                        pygame.mixer.music.play()
                    else:
                        pygame.mixer.music.unpause()
                    self.logger.info(f"Resumed pygame audio playback from {resume_pos:.2f}s")
                except Exception as e:
                    self.logger.warning(f"Failed to resume pygame playback: {e}")
            
            # Update memory state
            await self.memory_manager.set_tts_playback_state(
                audio_path=self.current_audio_path,
                status="playing",
                playback_position=resume_pos
            )
            
            self.logger.info(
                "TTS playback resumed",
                action="resume_tts_playback",
                output_data={"resume_position": resume_pos},
                layer="tts_interrupt_monitor"
            )
            
            return StateOutput(
                status=StatusType.SUCCESS,
                message="TTS playback resumed",
                code=StatusCode.OK,
                outputs={
                    "resumed": True,
                    "playback_position": resume_pos
                },
                meta={"playback_state": "playing"}
            )
            
        except Exception as e:
            self.logger.error(
                "Error resuming TTS playback",
                action="resume_tts_playback",
                reason=str(e),
                layer="tts_interrupt_monitor"
            )
            
            return StateOutput(
                status=StatusType.ERROR,
                message=f"Failed to resume TTS playback: {str(e)}",
                code=StatusCode.INTERNAL_ERROR,
                outputs={},
                meta={"error": str(e)}
            )
    
    async def stop_monitoring(self) -> StateOutput:
        """
        Stop TTS monitoring and playback.
        
        Returns:
            StateOutput with stop status
        """
        try:
            self.monitoring_active = False
            self.stop_monitoring_event.set()
            self.is_playing = False
            self.is_paused = False
            
            # Stop pygame playback if available
            if PYGAME_AVAILABLE:
                try:
                    pygame.mixer.music.stop()
                    self.logger.info("Stopped pygame audio playback")
                except Exception as e:
                    self.logger.warning(f"Failed to stop pygame playback: {e}")
            
            # Wait for monitor thread to finish
            if self.monitor_thread and self.monitor_thread.is_alive():
                self.monitor_thread.join(timeout=2.0)
            
            # Update memory state
            await self.memory_manager.set_tts_playback_state(
                audio_path=self.current_audio_path,
                status="stopped",
                playback_position=self.get_current_playback_position()
            )
            
            self.logger.info(
                "TTS monitoring stopped",
                action="stop_monitoring",
                layer="tts_interrupt_monitor"
            )
            
            return StateOutput(
                status=StatusType.SUCCESS,
                message="TTS monitoring stopped",
                code=StatusCode.OK,
                outputs={"monitoring_stopped": True},
                meta={"tts_monitor": "stopped"}
            )
            
        except Exception as e:
            self.logger.error(
                "Error stopping TTS monitoring",
                action="stop_monitoring",
                reason=str(e),
                layer="tts_interrupt_monitor"
            )
            
            return StateOutput(
                status=StatusType.ERROR,
                message=f"Failed to stop TTS monitoring: {str(e)}",
                code=StatusCode.INTERNAL_ERROR,
                outputs={},
                meta={"error": str(e)}
            )
    
    def get_current_playback_position(self) -> float:
        """Get current playback position in seconds."""
        if not self.playback_start_time:
            return 0.0
        
        elapsed = time.time() - self.playback_start_time - self.total_pause_duration
        if self.is_paused and self.pause_time:
            elapsed -= (time.time() - self.pause_time)
        
        return max(0.0, elapsed)
    
    async def start_tts_interrupt_monitoring(self, tts_result: StateOutput, state_manager=None):
        """
        Start real-time interrupt monitoring for TTS playback.

        This method implements:
        1. Background audio playback
        2. Continuous microphone input monitoring
        3. Real-time VAD processing
        4. Automatic interrupt triggering
        """
        try:
            self.logger.info(
                "Starting real-time TTS interrupt monitoring",
                action="start_tts_interrupt_monitoring",
                input_data={"tts_audio_path": tts_result.outputs.get("audio_path")},
                layer="tts_interrupt_monitor"
            )

            # Check environment variable first (highest priority)
            import os
            interrupt_detection_enabled = os.getenv('INTERRUPT_DETECTION_ENABLED', 'true').lower() == 'true'

            if not interrupt_detection_enabled:
                self.logger.info(
                    "🔇 Interrupt detection DISABLED via environment variable",
                    action="start_tts_interrupt_monitoring",
                    output_data={"interrupt_enabled": False, "reason": "INTERRUPT_DETECTION_ENABLED=false"},
                    layer="tts_interrupt_monitor"
                )

                audio_path = tts_result.outputs.get("audio_path")
                if audio_path:
                    await self._play_audio_without_monitoring(audio_path)

                return StateOutput(
                    status=StatusType.SUCCESS,
                    message="TTS audio played without interrupt monitoring (disabled via env var)",
                    code=StatusCode.OK,
                    outputs={"monitoring_skipped": True, "interrupt_enabled": False, "audio_played": True},
                    meta={"interrupt_detection_enabled": False, "disabled_reason": "environment_variable"}
                )

            # Get interrupt configuration from state_manager if available, otherwise use instance config
            if state_manager and hasattr(state_manager, 'interrupt_config') and state_manager.interrupt_config:
                interrupt_config = state_manager.interrupt_config
            elif self.interrupt_config:
                interrupt_config = self.interrupt_config
            else:
                interrupt_config = get_interrupt_config()

            audio_path = tts_result.outputs.get("audio_path")
            if not audio_path:
                self.logger.warning("No audio path provided for TTS playback")
                return StateOutput(
                    status=StatusType.ERROR,
                    message="No audio path provided",
                    code=StatusCode.BAD_REQUEST,
                    outputs={},
                    meta={}
                )

            # Check if interrupts are disabled in configuration
            if interrupt_config and not interrupt_config.global_settings.enabled:
                self.logger.info(
                    "Interrupt detection is disabled - playing TTS audio without interrupt monitoring",
                    action="start_tts_interrupt_monitoring",
                    output_data={"interrupt_enabled": False, "audio_path": audio_path},
                    layer="tts_interrupt_monitor"
                )

                # Play audio without interrupt monitoring
                await self._play_audio_without_monitoring(audio_path)

                return StateOutput(
                    status=StatusType.SUCCESS,
                    message="TTS audio played without interrupt monitoring",
                    code=StatusCode.OK,
                    outputs={"monitoring_skipped": True, "interrupt_enabled": False, "audio_played": True},
                    meta={"interrupt_detection_enabled": False}
                )

            # Create InterruptState instance
            interrupt_state = InterruptState(
                state_id="interrupt_monitor",
                agent_registry=state_manager.agent_registry if state_manager else None,
                session_id=self.session_id,
                interrupt_config=interrupt_config
            )

            # Audio path already extracted above, continue with interrupt monitoring

            # Store TTS playback state for potential interruption
            await self.memory_manager.set_tts_playback_state(
                audio_path=audio_path,
                status="playing",
                playback_position=0.0,
                message_hash=None
            )

            # Start background audio playback and interrupt monitoring
            await self.run_tts_with_interrupt_monitoring(audio_path, interrupt_state, interrupt_config, state_manager)

            self.logger.info(
                "Real-time TTS interrupt monitoring started",
                action="start_tts_interrupt_monitoring",
                output_data={"monitoring_active": True, "audio_path": audio_path},
                layer="tts_interrupt_monitor"
            )

        except Exception as e:
            self.logger.error(
                "Error starting TTS interrupt monitoring",
                action="start_tts_interrupt_monitoring",
                reason=str(e),
                layer="tts_interrupt_monitor"
            )

    async def _play_audio_without_monitoring(self, audio_path: str):
        """
        Play TTS audio without interrupt monitoring.

        This method provides simple audio playback when interrupt detection is disabled.
        """
        try:
            self.logger.info(
                "Playing TTS audio without interrupt monitoring",
                action="_play_audio_without_monitoring",
                input_data={"audio_path": audio_path},
                layer="tts_interrupt_monitor"
            )

            # Store TTS playback state in memory
            await self.memory_manager.set_tts_playback_state(
                audio_path=audio_path,
                status="playing",
                playback_position=0.0
            )

            # Use pygame for audio playback if available
            if PYGAME_AVAILABLE:
                try:
                    import pygame
                    pygame.mixer.quit()  # Clean up any existing mixer
                    pygame.mixer.init(frequency=22050, size=-16, channels=2, buffer=512)
                    pygame.mixer.music.load(audio_path)
                    pygame.mixer.music.play()

                    self.logger.info(f"🔊 Started pygame audio playback (no monitoring): {audio_path}")

                    # Wait for playback to complete
                    import asyncio
                    while pygame.mixer.music.get_busy():
                        await asyncio.sleep(0.1)

                    self.logger.info("✅ Audio playback completed")
                    pygame.mixer.quit()

                except Exception as e:
                    self.logger.error(f"❌ Failed to play audio with pygame: {e}")
                    # Fallback to playsound if pygame fails
                    await self._fallback_audio_playback(audio_path)
            else:
                # Fallback to playsound if pygame not available
                self.logger.info("Pygame not available, using playsound fallback")
                await self._fallback_audio_playback(audio_path)

            # Update memory state to completed
            await self.memory_manager.set_tts_playback_state(
                audio_path=audio_path,
                status="completed",
                playback_position=0.0
            )

        except Exception as e:
            self.logger.error(
                "Error playing audio without monitoring",
                action="_play_audio_without_monitoring",
                reason=str(e),
                layer="tts_interrupt_monitor"
            )
            raise

    async def _fallback_audio_playback(self, audio_path: str):
        """Fallback audio playback using playsound."""
        try:
            self.logger.info(f"🔊 Using playsound for audio playback: {audio_path}")

            # Try importing playsound
            try:
                from playsound import playsound

                # Fix Windows path issues by normalizing the path
                import os
                normalized_path = os.path.normpath(audio_path)

                self.logger.info(f"🔊 Playing audio with playsound: {normalized_path}")

                # Run playsound in a thread to avoid blocking
                await asyncio.to_thread(playsound, normalized_path)
                self.logger.info("✅ Playsound audio playback completed")

            except ImportError:
                self.logger.error("❌ playsound not installed. Install with: pip install playsound")
                print(f"❌ playsound not installed. Please install with: pip install playsound")
                print(f"   Or play manually: {audio_path}")

            except Exception as playsound_error:
                self.logger.error(f"❌ playsound failed: {playsound_error}")
                print(f"❌ Audio playback failed: {playsound_error}")
                print(f"   Please play manually: {audio_path}")

                # Try alternative: open with default system player
                try:
                    import os
                    import subprocess
                    if os.name == 'nt':  # Windows
                        os.startfile(audio_path)
                        self.logger.info("✅ Opened audio with default Windows player")
                    else:  # macOS/Linux
                        subprocess.run(['open' if os.name == 'posix' else 'xdg-open', audio_path])
                        self.logger.info("✅ Opened audio with default system player")
                except Exception as system_error:
                    self.logger.error(f"❌ System player also failed: {system_error}")
                    print(f"❌ All audio playback methods failed. Please play manually: {audio_path}")

        except Exception as e:
            self.logger.error(f"❌ Fallback audio playback failed: {e}")
            print(f"❌ Audio file generated but playback failed: {audio_path}")
            print(f"   Please play the file manually to hear the response.")



    async def run_tts_with_interrupt_monitoring(self, audio_path: str, interrupt_state: InterruptState,
                                              interrupt_config, state_manager=None):
        """
        Run TTS playback with concurrent interrupt monitoring.
        Fixed version that actually works and plays audio.
        """
        try:
            self.logger.info(f"Starting TTS playback with interrupt monitoring: {audio_path}")

            # Use pygame for audio playback if available
            if PYGAME_AVAILABLE:
                try:
                    import pygame
                    pygame.mixer.quit()  # Clean up any existing mixer
                    pygame.mixer.init(frequency=22050, size=-16, channels=2, buffer=512)
                    pygame.mixer.music.load(audio_path)
                    pygame.mixer.music.play()

                    self.logger.info(f"🔊 Playing audio with pygame: {audio_path}")

                    # FIXED: Implement actual interrupt monitoring during playback
                    import asyncio
                    import sounddevice as sd
                    import numpy as np
                    import threading
                    import time

                    # Initialize interrupt monitoring
                    interrupt_detected = False
                    sample_rate = 16000
                    chunk_duration = 0.1  # 100ms chunks
                    chunk_size = int(sample_rate * chunk_duration)

                    # Get VAD threshold from config
                    vad_threshold = 0.5
                    if interrupt_config and hasattr(interrupt_config, 'global_settings'):
                        vad_threshold = interrupt_config.global_settings.vad_threshold

                    self.logger.info(f"🎤 Starting interrupt monitoring (threshold: {vad_threshold})")

                    # Monitor for interrupts while audio is playing
                    while pygame.mixer.music.get_busy() and not interrupt_detected:
                        try:
                            # Record audio chunk from microphone
                            audio_chunk = sd.rec(chunk_size, samplerate=sample_rate, channels=1, dtype='int16')
                            sd.wait()
                            audio_bytes = audio_chunk.tobytes()

                            # Use our enhanced VAD system for interrupt detection
                            from utils.audio_utils import RobustVADInterruptHandler
                            if not hasattr(self, '_vad_handler'):
                                self._vad_handler = RobustVADInterruptHandler(interrupt_config)

                            # Process audio frame for interrupt detection
                            vad_result = self._vad_handler.process_frame(audio_bytes, sample_rate)

                            if vad_result == "INTERRUPT_START":
                                self.logger.info("🛑 INTERRUPT DETECTED! Pausing TTS...")
                                pygame.mixer.music.pause()
                                interrupt_detected = True

                                # Handle the interrupt
                                if state_manager:
                                    await self._handle_interrupt_during_playback(
                                        audio_path,
                                        pygame.mixer.music.get_pos() / 1000.0,  # Convert to seconds
                                        interrupt_state,
                                        state_manager
                                    )
                                break

                            # Small delay to prevent excessive CPU usage
                            await asyncio.sleep(0.05)

                        except Exception as e:
                            self.logger.warning(f"Error during interrupt monitoring: {e}")
                            await asyncio.sleep(0.1)

                    if not interrupt_detected:
                        self.logger.info("✅ Audio playback completed without interruption")

                    pygame.mixer.quit()

                except Exception as e:
                    self.logger.error(f"Pygame playback failed: {e}")
                    # Fallback to playsound
                    await self._fallback_audio_playback(audio_path)
            else:
                # Use playsound fallback
                self.logger.info("Pygame not available, using playsound fallback")
                await self._fallback_audio_playback(audio_path)

            # Update TTS state
            await self.memory_manager.set_tts_playback_state(
                audio_path=audio_path,
                status="completed",
                playback_position=0.0,
                message_hash=None
            )

        except Exception as e:
            self.logger.error(f"Error in TTS playback: {e}")
            # Still try to play audio even if there's an error
            try:
                await self._fallback_audio_playback(audio_path)
            except Exception as fallback_error:
                self.logger.error(f"Fallback audio playback also failed: {fallback_error}")
                self.logger.warning(f"Audio file generated but playback failed: {audio_path}")
                print(f"❌ Audio playback failed. Please play manually: {audio_path}")

    def _calculate_adaptive_thresholds(self, baseline_rms: float, feedback_confidence: float) -> dict:
        """
        Calculate adaptive thresholds for enhanced feedback detection.

        Args:
            baseline_rms: Current baseline RMS level
            feedback_confidence: Current feedback detection confidence (0.0-1.0)

        Returns:
            dict: Adaptive thresholds for various detection criteria
        """
        base_peak_rms = 12.0
        base_energy = 30000000
        base_rms_multiplier = 3.0

        return {
            'peak_rms_threshold': base_peak_rms + (feedback_confidence * 3.0),
            'energy_threshold': base_energy * (1.0 + feedback_confidence),
            'rms_spike_threshold': baseline_rms * (base_rms_multiplier + feedback_confidence),
            'isolation_delay_multiplier': 1.0 + (feedback_confidence * 0.5)
        }



    async def _handle_interrupt_during_playback(self, audio_path: str, playback_position: float, state_manager=None):
        """Handle interrupt that occurred during TTS playback."""
        try:
            import pygame
            import asyncio

            self.logger.info("Handling interrupt...")
            self.logger.info("Capturing real user audio during interrupt...")

            # Capture real audio from microphone during interrupt
            captured_user_input = await self.capture_and_transcribe_interrupt_audio()
            self.logger.info(f"Transcribed user input: '{captured_user_input}'")

            # Handle interrupt through state manager if available
            if state_manager and hasattr(state_manager, 'interrupt_manager'):
                ack_result = await state_manager.interrupt_manager.handle_interrupt(
                    audio_data=None,
                    current_tts_audio_path=audio_path,
                    playback_position=playback_position,
                    user_input=captured_user_input,
                    state_manager=state_manager
                )
                ack_msg = ack_result.outputs.get("acknowledgment_message")
                self.logger.info(f"Ack message: {ack_msg}")

                # Only play acknowledgment if we have a valid message
                if ack_msg and ack_msg.strip():
                    # Set flag to skip interrupt monitoring for acknowledgment TTS
                    if hasattr(state_manager, 'skip_interrupt_monitoring'):
                        state_manager.skip_interrupt_monitoring = True
                    try:
                        tts_ack_result = await state_manager.executePipelineState({"text": ack_msg})
                        ack_audio_path = tts_ack_result.outputs.get("audio_path")
                        if ack_audio_path:
                            self.logger.info("Playing acknowledgment TTS...")
                            pygame.mixer.music.load(ack_audio_path)
                            pygame.mixer.music.play()
                            while pygame.mixer.music.get_busy():
                                await asyncio.sleep(0.1)
                            self.logger.info("Acknowledgment TTS finished.")
                    finally:
                        # Reset flag after acknowledgment TTS
                        if hasattr(state_manager, 'skip_interrupt_monitoring'):
                            state_manager.skip_interrupt_monitoring = False

                # Set queue flags for processing user input after TTS completion
                interrupt_context = await self.memory_manager.get_interrupt_context()
                user_input_queued = interrupt_context.get("user_input_queued")
                action_reversible = interrupt_context.get("action_reversible", True)

                if user_input_queued and user_input_queued not in ["[Interrupt detected]", "[User interrupted during TTS]"]:
                    if action_reversible:
                        self.logger.info(f"Queuing user input for after TTS: '{user_input_queued}'")
                        await self.memory_manager.set("contextual", "queued_user_input", user_input_queued)
                        await self.memory_manager.set("contextual", "process_queued_after_tts", True)
                    else:
                        self.logger.info(f"Irreversible action - no queuing for: '{user_input_queued}'")

                self.logger.info(f"Resuming original TTS from {playback_position:.2f} seconds...")
                pygame.mixer.music.load(audio_path)
                pygame.mixer.music.play(start=playback_position)
                while pygame.mixer.music.get_busy():
                    await asyncio.sleep(0.1)
                self.logger.info("Original TTS playback completed.")

                # Check if there's queued user input to process (for reversible actions)
                if hasattr(state_manager, '_process_queued_user_input_after_tts'):
                    await state_manager._process_queued_user_input_after_tts()

        except Exception as e:
            self.logger.error(
                "Error handling interrupt during playback",
                action="_handle_interrupt_during_playback",
                reason=str(e),
                layer="tts_interrupt_monitor"
            )

    async def process_audio_chunk_for_interrupt(self, audio_data: bytes, interrupt_state: InterruptState) -> bool:
        """
        Process audio chunk for interrupt detection using VAD.

        Returns:
            bool: True if voice activity detected, False otherwise
        """
        try:
            # Use InterruptState's VAD functionality
            voice_detected = await interrupt_state._detect_voice_activity(audio_data)
            return voice_detected

        except Exception as e:
            self.logger.error(
                "Error processing audio chunk for interrupt",
                action="process_audio_chunk_for_interrupt",
                reason=str(e),
                layer="tts_interrupt_monitor"
            )
            return False

    async def capture_and_transcribe_interrupt_audio(self, duration_sec: float = 5.0, sample_rate: int = 16000) -> str:
        """
        Capture real audio from microphone during interrupt and transcribe it to text.

        Args:
            duration_sec: Duration to record audio in seconds
            sample_rate: Audio sample rate

        Returns:
            str: Transcribed text from the captured audio
        """
        try:
            import sounddevice as sd
            import numpy as np
            import tempfile
            import wave
            import os

            self.logger.info(f"Recording {duration_sec} seconds of interrupt audio...")

            # Record audio from microphone
            audio_data = sd.rec(
                int(duration_sec * sample_rate),
                samplerate=sample_rate,
                channels=1,
                dtype='int16'
            )
            sd.wait()  # Wait for recording to complete

            # --- Refactored: Use save_interrupt_audio from interrupt_manager.py ---
            session_id = getattr(self, 'session_id', 'default')
            save_interrupt_audio(audio_data.tobytes(), session_id, sample_rate)
            self.logger.info(f"Interrupt audio saved using save_interrupt_audio for session: {session_id}")

            # Save to temporary WAV file for STT processing
            with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_file:
                temp_audio_path = temp_file.name

            # Write WAV file
            with wave.open(temp_audio_path, 'wb') as wf:
                wf.setnchannels(1)
                wf.setsampwidth(2)  # 16-bit
                wf.setframerate(sample_rate)
                wf.writeframes(audio_data.tobytes())

            self.logger.info("Audio captured, transcribing...")

            # Transcribe the captured audio using STT agent
            transcribed_text = await self._transcribe_audio_file(temp_audio_path)
            self.logger.info(f"Transcription result: '{transcribed_text}'")

            # Clean up temporary file
            try:
                os.unlink(temp_audio_path)
            except:
                pass  # Ignore cleanup errors

            return transcribed_text

        except ImportError:
            self.logger.error("sounddevice not available - install with: pip install sounddevice")
            return "[Audio capture not available]"
        except Exception as e:
            self.logger.error(f"Audio capture failed: {e}")
            return f"[Audio capture error: {str(e)}]"

    async def _transcribe_audio_file(self, audio_file_path: str) -> str:
        """
        Transcribe an audio file using the STT agent.

        Args:
            audio_file_path: Path to the audio file to transcribe

        Returns:
            str: Transcribed text from the audio file
        """
        try:
            # Get agent registry from memory manager or use a direct approach
            agent_registry = getattr(self, 'agent_registry', None)

            if not agent_registry:
                # Try to get it from memory manager if available
                try:
                    # Check if we can access agent registry through memory manager
                    session_data = await self.memory_manager.get("session_metadata")
                    if session_data and "agent_registry" in session_data:
                        agent_registry = session_data["agent_registry"]
                except:
                    pass

            # --- FIX: Use getAgent method instead of key membership ---
            stt_agent = None
            if agent_registry and hasattr(agent_registry, "getAgent"):
                stt_agent = agent_registry.getAgent("stt_agent")

            if stt_agent:
                self.logger.info("Using STT agent to transcribe interrupt audio")

                # Prepare input for STT agent
                stt_input = {
                    "audio_path": audio_file_path,
                    "language": "en"  # Default to English
                }

                # Call STT agent
                stt_result = await stt_agent.process(stt_input, {})

                if stt_result.status.value == 'success':
                    transcribed_text = stt_result.outputs.get('text', '').strip()
                    if transcribed_text:
                        self.logger.info(f"STT transcription successful: '{transcribed_text}'")
                        return transcribed_text
                    else:
                        self.logger.warning("STT returned empty transcription")
                        return "[No speech detected in interrupt audio]"
                else:
                    self.logger.error(f"STT agent failed: {stt_result.message}")
                    return f"[STT error: {stt_result.message}]"
            else:
                # Fallback: Try to use a direct STT approach
                self.logger.warning("No STT agent available, attempting direct transcription")
                return await self._fallback_transcription(audio_file_path)

        except Exception as e:
            self.logger.error(f"Audio transcription failed: {e}")
            return f"[Transcription error: {str(e)}]"

    async def _fallback_transcription(self, audio_file_path: str) -> str:
        """
        Fallback transcription method when STT agent is not available.

        Args:
            audio_file_path: Path to the audio file to transcribe

        Returns:
            str: Transcribed text or error message
        """
        try:
            # Try to use speech_recognition library as fallback
            import speech_recognition as sr

            recognizer = sr.Recognizer()

            with sr.AudioFile(audio_file_path) as source:
                audio = recognizer.record(source)

            # Try Google Speech Recognition (free tier)
            try:
                text = recognizer.recognize_google(audio)
                self.logger.info(f"Fallback transcription successful: '{text}'")
                return text.strip()
            except sr.UnknownValueError:
                self.logger.warning("Fallback STT could not understand audio")
                return "[No speech detected in interrupt audio]"
            except sr.RequestError as e:
                self.logger.error(f"Fallback STT service error: {e}")
                return f"[STT service error: {str(e)}]"

        except ImportError:
            self.logger.error("speech_recognition library not available - install with: pip install SpeechRecognition")
            return "[STT not available - install SpeechRecognition library]"
        except Exception as e:
            self.logger.error(f"Fallback transcription failed: {e}")
            return f"[Fallback transcription error: {str(e)}]"

    def _background_monitor_worker(self):
        """Background worker thread for interrupt monitoring."""
        try:
            while self.monitoring_active and not self.stop_monitoring_event.is_set():
                # Check for interrupt conditions
                # This is where you would integrate real VAD or other interrupt detection

                # For now, simulate checking every 100ms
                time.sleep(0.1)

                # Check if playback is complete
                if PYGAME_AVAILABLE and not pygame.mixer.music.get_busy() and self.is_playing:
                    self.is_playing = False
                    self.monitoring_active = False
                    break

        except Exception as e:
            self.logger.error(
                "Error in background monitor worker",
                action="_background_monitor_worker",
                reason=str(e),
                layer="tts_interrupt_monitor"
            )
