#!/usr/bin/env python3
"""
Install VAD Dependencies Script

This script installs the required dependencies for the enhanced VAD interrupt system.
It handles the installation of Silero VAD, WebRTC VAD, and other audio processing libraries.
"""

import subprocess
import sys
import os
from pathlib import Path

def run_command(command, description):
    """Run a command and handle errors."""
    print(f"📦 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed:")
        print(f"   Command: {command}")
        print(f"   Error: {e.stderr}")
        return False

def install_dependencies():
    """Install all required VAD dependencies."""
    print("🚀 Installing Enhanced VAD Dependencies")
    print("=" * 50)
    
    # Core audio processing dependencies
    dependencies = [
        ("pip install webrtcvad", "Installing WebRTC VAD"),
        ("pip install sounddevice", "Installing SoundDevice"),
        ("pip install scipy", "Installing SciPy for audio filtering"),
        ("pip install numpy", "Installing NumPy"),
    ]
    
    # Optional dependencies for better audio support
    optional_dependencies = [
        ("pip install pyaudio", "Installing PyAudio (optional)"),
        ("pip install pydub", "Installing PyDub for audio format support"),
        ("pip install librosa", "Installing Librosa for advanced audio processing"),
    ]
    
    success_count = 0
    total_count = len(dependencies)
    
    # Install core dependencies
    print("\n📋 Installing Core Dependencies:")
    for command, description in dependencies:
        if run_command(command, description):
            success_count += 1
        print()
    
    # Install optional dependencies (don't fail if these don't work)
    print("\n📋 Installing Optional Dependencies:")
    for command, description in optional_dependencies:
        run_command(command, description)
        print()
    
    # Summary
    print("📊 Installation Summary:")
    print(f"   Core dependencies: {success_count}/{total_count} successful")
    
    if success_count == total_count:
        print("✅ All core dependencies installed successfully!")
        print("\n🎯 Enhanced VAD system is ready to use with:")
        print("   • WebRTC VAD (production-ready)")
        print("   • Frequency filtering")
        print("   • Adaptive thresholding")
        print("   • Consensus-based detection")
        print("   • Noise floor adaptation")
        return True
    else:
        print(f"⚠️  {total_count - success_count} core dependencies failed to install")
        print("   The system may still work with reduced functionality")
        return False

def verify_installation():
    """Verify that the key dependencies are working."""
    print("\n🔍 Verifying Installation:")
    
    # Test imports
    tests = [
        ("webrtcvad", "import webrtcvad; print('WebRTC VAD version:', webrtcvad.__version__ if hasattr(webrtcvad, '__version__') else 'installed')"),
        ("sounddevice", "import sounddevice as sd; print('SoundDevice version:', sd.__version__)"),
        ("scipy", "import scipy; print('SciPy version:', scipy.__version__)"),
        ("numpy", "import numpy as np; print('NumPy version:', np.__version__)"),
    ]
    
    for name, test_code in tests:
        try:
            result = subprocess.run([sys.executable, "-c", test_code], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                print(f"✅ {name}: {result.stdout.strip()}")
            else:
                print(f"❌ {name}: Failed to import")
        except Exception as e:
            print(f"❌ {name}: Error - {e}")

def create_requirements_file():
    """Create a clean requirements.txt file for the VAD system."""
    requirements_content = """# Enhanced VAD Interrupt System Dependencies
# Core audio processing
webrtcvad>=2.0.10
sounddevice>=0.5.2
scipy>=1.16.0
numpy>=2.3.0

# Optional audio processing
pyaudio>=0.2.11
pydub>=0.25.1
librosa>=0.10.0

# Existing project dependencies
aiofiles==24.1.0
annotated-types==0.7.0
anyio==4.9.0
asteval==1.0.6
cachetools==5.5.2
certifi==2025.6.15
charset-normalizer==3.4.2
colorama==0.4.6
distro==1.9.0
dnspython==2.7.0
elevenlabs==2.5.0
google-api-core==2.25.1
google-auth==2.40.3
google-cloud-texttospeech==2.27.0
googleapis-common-protos==1.70.0
grpcio==1.73.1
grpcio-status==1.73.1
h11==0.16.0
h2==4.2.0
hpack==4.1.0
httpcore==1.0.9
httpx==0.28.1
hyperframe==6.1.0
idna==3.10
jiter==0.10.0
motor==3.7.1
openai==1.86.0
portalocker==2.10.1
proto-plus==1.26.1
protobuf==6.31.1
pyasn1==0.6.1
pyasn1_modules==0.4.2
pydantic==2.11.7
pydantic_core==2.33.2
pymongo==4.13.2
python-dotenv==1.1.0
qdrant-client==1.14.3
redis==6.2.0
requests==2.32.4
rsa==4.9.1
setuptools==78.1.1
sniffio==1.3.1
tenacity==9.1.2
tqdm==4.67.1
typing-inspection==0.4.1
typing_extensions==4.14.0
urllib3==2.4.0
websockets==15.0.1
wheel==0.45.1
"""
    
    with open("requirements_vad.txt", "w") as f:
        f.write(requirements_content)
    
    print("📄 Created requirements_vad.txt with all dependencies")

if __name__ == "__main__":
    print("🎯 Enhanced VAD Interrupt System - Dependency Installer")
    print("This script will install all required dependencies for the improved interrupt system")
    print()
    
    # Create requirements file
    create_requirements_file()
    
    # Install dependencies
    success = install_dependencies()
    
    # Verify installation
    verify_installation()
    
    if success:
        print("\n🎉 Installation completed successfully!")
        print("\n📋 Next Steps:")
        print("1. Update your interrupt configuration files")
        print("2. Test the enhanced VAD system")
        print("3. Adjust thresholds based on your environment")
        print("\n💡 Configuration Tips:")
        print("• Use 'webrtcvad' with aggressiveness=2 for balanced detection")
        print("• Set consensus_frames_required=3 for robust detection")
        print("• Enable frequency_filtering for noisy environments")
        print("• Use adaptive_threshold for varying noise conditions")
        print("• Adjust webrtc_aggressiveness (0-3) based on environment")
    else:
        print("\n⚠️  Installation completed with some issues")
        print("Check the error messages above and install missing dependencies manually")
    
    print(f"\n📁 Working directory: {os.getcwd()}")
    print("📄 Requirements file: requirements_vad.txt")
