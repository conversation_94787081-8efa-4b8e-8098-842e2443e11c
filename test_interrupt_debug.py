#!/usr/bin/env python3
"""
Debug Interrupt System - Find out why interrupts aren't working
"""

import os
import sys
import asyncio
import time
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.append(str(project_root))

# Set WORKING interrupt settings (not the ultra-conservative ones)
os.environ['VAD_THRESHOLD'] = '0.3'  # Lower threshold for easier triggering
os.environ['VAD_METHOD'] = 'webrtcvad'
os.environ['WEBRTC_AGGRESSIVENESS'] = '2'  # Balanced
os.environ['CONSENSUS_FRAMES_REQUIRED'] = '3'  # Our enhanced VAD setting
os.environ['SILENCE_FRAMES_REQUIRED'] = '8'  # Our enhanced VAD setting
os.environ['TTS_INTERRUPT_COOLDOWN_SECONDS'] = '0.0'  # No cooldown
os.environ['REQUIRED_CONSECUTIVE_FRAMES'] = '3'  # Much lower than 50!
os.environ['IMMEDIATE_INTERRUPT_MODE'] = 'true'
os.environ['VAD_DEBUG_MODE'] = 'true'
os.environ['INTERRUPT_DETECTION_ENABLED'] = 'true'  # Make sure it's enabled

print("🔧 DEBUG: Setting WORKING interrupt settings...")
print(f"   VAD_THRESHOLD: {os.environ['VAD_THRESHOLD']} (0.3 = reasonable)")
print(f"   CONSENSUS_FRAMES_REQUIRED: {os.environ['CONSENSUS_FRAMES_REQUIRED']} (3 = our enhanced VAD)")
print(f"   REQUIRED_CONSECUTIVE_FRAMES: {os.environ['REQUIRED_CONSECUTIVE_FRAMES']} (3 vs 50 in test!)")
print(f"   TTS_INTERRUPT_COOLDOWN_SECONDS: {os.environ['TTS_INTERRUPT_COOLDOWN_SECONDS']} (0 = no delay)")
print()

async def test_tts_interrupt_monitoring():
    """Test if TTS interrupt monitoring is actually being called."""
    print("🎯 Testing TTS Interrupt Monitoring Integration")
    print("=" * 60)
    
    try:
        # Import the TTS interrupt monitor
        from core.interruption.tts_interrupt_monitor import TTSInterruptMonitor
        from core.config.interrupt_config import get_interrupt_config
        from schemas.outputSchema import StateOutput, StatusType, StatusCode
        
        # Create a mock memory manager
        class MockMemoryManager:
            async def set_tts_playback_state(self, **kwargs):
                print(f"   📝 TTS state updated: {kwargs}")
        
        # Initialize interrupt config and monitor
        interrupt_config = get_interrupt_config()
        memory_manager = MockMemoryManager()
        session_id = "test_session"
        
        monitor = TTSInterruptMonitor(session_id, memory_manager, interrupt_config)
        
        print("✅ TTS Interrupt Monitor initialized")
        print(f"   Interrupt enabled: {interrupt_config.global_settings.enabled}")
        print(f"   VAD threshold: {interrupt_config.global_settings.vad_threshold}")
        print(f"   VAD method: {interrupt_config.global_settings.vad_method}")
        print()
        
        # Create a mock TTS result (simulate TTS generating audio)
        mock_tts_result = StateOutput(
            status=StatusType.SUCCESS,
            message="TTS completed",
            code=StatusCode.OK,
            outputs={"audio_path": "test_audio.wav"}  # This would be a real audio file
        )
        
        print("🎵 Simulating TTS playback with interrupt monitoring...")
        print("   (This would normally play actual TTS audio)")
        print("   Speak into your microphone to test interrupts!")
        print()
        
        # Test the interrupt monitoring (this should call our enhanced VAD)
        try:
            # This should trigger the interrupt monitoring we added
            await monitor.start_tts_interrupt_monitoring(mock_tts_result)
            print("✅ TTS interrupt monitoring completed")
        except Exception as e:
            print(f"❌ TTS interrupt monitoring failed: {e}")
            import traceback
            traceback.print_exc()
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_interrupt_config():
    """Test if interrupt configuration is loaded correctly."""
    print("\n🔧 Testing Interrupt Configuration")
    print("=" * 60)
    
    try:
        from core.config.interrupt_config import get_interrupt_config
        
        config = get_interrupt_config()
        
        print("✅ Interrupt configuration loaded:")
        print(f"   Enabled: {config.global_settings.enabled}")
        print(f"   VAD Method: {config.global_settings.vad_method}")
        print(f"   VAD Threshold: {config.global_settings.vad_threshold}")
        print(f"   Consensus Frames: {getattr(config.global_settings, 'consensus_frames_required', 'NOT SET')}")
        print(f"   Silence Frames: {getattr(config.global_settings, 'silence_frames_required', 'NOT SET')}")
        print(f"   WebRTC Aggressiveness: {getattr(config.global_settings, 'webrtc_aggressiveness', 'NOT SET')}")
        
        # Check if our enhanced VAD settings are being used
        if hasattr(config.global_settings, 'consensus_frames_required'):
            print("✅ Enhanced VAD settings detected!")
        else:
            print("⚠️  Enhanced VAD settings NOT detected - using fallback")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_enhanced_vad_direct():
    """Test enhanced VAD directly."""
    print("\n🎯 Testing Enhanced VAD Directly")
    print("=" * 60)
    
    try:
        from utils.audio_utils import RobustVADInterruptHandler
        from core.config.interrupt_config import get_interrupt_config
        import sounddevice as sd
        import numpy as np
        
        config = get_interrupt_config()
        vad_handler = RobustVADInterruptHandler(config)
        
        print("✅ Enhanced VAD handler created")
        print("🎤 Testing with live microphone...")
        print("   Speak now to test VAD detection!")
        
        sample_rate = 16000
        chunk_duration = 0.1
        chunk_size = int(sample_rate * chunk_duration)
        
        for i in range(30):  # Test for 3 seconds
            try:
                # Record audio chunk
                audio_chunk = sd.rec(chunk_size, samplerate=sample_rate, channels=1, dtype='int16')
                sd.wait()
                audio_bytes = audio_chunk.tobytes()
                
                # Test enhanced VAD
                result = vad_handler.process_frame(audio_bytes, sample_rate)
                
                if result == "INTERRUPT_START":
                    print(f"🛑 ENHANCED VAD DETECTED SPEECH! (frame {i})")
                    return True
                elif result == "NO_CHANGE":
                    if i % 10 == 0:
                        print(f"   Monitoring... (frame {i})")
                
                await asyncio.sleep(0.05)
                
            except Exception as e:
                print(f"❌ VAD test error: {e}")
                break
        
        print("⚠️  No speech detected during test")
        return False
        
    except Exception as e:
        print(f"❌ Enhanced VAD test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run comprehensive interrupt debugging."""
    print("🚀 Interrupt System Debug Test")
    print("=" * 60)
    print()
    
    # Test 1: Configuration
    config_ok = await test_interrupt_config()
    
    # Test 2: Enhanced VAD directly
    vad_ok = await test_enhanced_vad_direct()
    
    # Test 3: TTS interrupt monitoring
    tts_ok = await test_tts_interrupt_monitoring()
    
    # Summary
    print("\n🎉 DEBUG TEST SUMMARY")
    print("=" * 60)
    print(f"✅ Configuration loading: {'PASS' if config_ok else 'FAIL'}")
    print(f"✅ Enhanced VAD detection: {'PASS' if vad_ok else 'FAIL'}")
    print(f"✅ TTS interrupt monitoring: {'PASS' if tts_ok else 'FAIL'}")
    
    if not vad_ok:
        print("\n🔧 DIAGNOSIS: Enhanced VAD not detecting speech")
        print("   Possible causes:")
        print("   1. Microphone not working or too quiet")
        print("   2. VAD threshold too high")
        print("   3. Audio permissions not granted")
        print("   4. Wrong audio device selected")
        
    if not tts_ok:
        print("\n🔧 DIAGNOSIS: TTS interrupt monitoring not working")
        print("   Possible causes:")
        print("   1. Interrupt monitoring not being called")
        print("   2. Audio playback method not using interrupt monitoring")
        print("   3. Configuration issues")
        
    print("\n💡 RECOMMENDATIONS:")
    print("   1. Check microphone permissions and volume")
    print("   2. Use lower VAD threshold (0.1-0.3)")
    print("   3. Verify interrupt monitoring is enabled")
    print("   4. Check if TTS actually calls interrupt monitoring")

if __name__ == "__main__":
    print("🎯 Starting Interrupt System Debug...")
    print()
    
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⏹️  Debug interrupted by user")
    except Exception as e:
        print(f"\n❌ Debug failed with error: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n✨ Debug completed!")
