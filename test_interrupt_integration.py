#!/usr/bin/env python3
"""
Test Enhanced VAD Integration with Interrupt System

This script tests if the enhanced VAD system works with the actual interrupt monitoring.
"""

import os
import sys
import asyncio
import time
import threading
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.append(str(project_root))

# Set enhanced VAD environment variables
os.environ.setdefault('VAD_METHOD', 'webrtcvad')
os.environ.setdefault('VAD_THRESHOLD', '0.5')
os.environ.setdefault('CONSENSUS_FRAMES_REQUIRED', '3')
os.environ.setdefault('SILENCE_FRAMES_REQUIRED', '8')
os.environ.setdefault('WEBRTC_AGGRESSIVENESS', '2')
os.environ.setdefault('FREQUENCY_FILTERING', 'true')
os.environ.setdefault('ADAPTIVE_THRESHOLD', 'true')
os.environ.setdefault('VAD_DEBUG_MODE', 'true')

async def test_real_time_interrupt_detection():
    """Test real-time interrupt detection with enhanced VAD."""
    print("🎯 Testing Real-Time Interrupt Detection with Enhanced VAD")
    print("=" * 60)
    
    try:
        # Import required modules
        from utils.audio_utils import RobustVADInterruptHandler
        from core.config.interrupt_config import get_interrupt_config
        import sounddevice as sd
        import numpy as np
        
        # Initialize enhanced VAD handler
        interrupt_config = get_interrupt_config()
        vad_handler = RobustVADInterruptHandler(interrupt_config)
        
        print("✅ Enhanced VAD handler initialized")
        print(f"   VAD Method: {interrupt_config.global_settings.vad_method}")
        print(f"   VAD Threshold: {interrupt_config.global_settings.vad_threshold}")
        print(f"   Consensus Frames: {interrupt_config.global_settings.consensus_frames_required}")
        print(f"   Silence Frames: {interrupt_config.global_settings.silence_frames_required}")
        print()
        
        # Audio parameters
        sample_rate = 16000
        chunk_duration = 0.1  # 100ms chunks
        chunk_size = int(sample_rate * chunk_duration)
        
        print("🎤 Starting real-time microphone monitoring...")
        print("   Speak into your microphone to test interrupt detection")
        print("   Press Ctrl+C to stop")
        print()
        
        interrupt_detected = False
        frame_count = 0
        
        while not interrupt_detected:
            try:
                # Record audio chunk from microphone
                audio_chunk = sd.rec(chunk_size, samplerate=sample_rate, channels=1, dtype='int16')
                sd.wait()
                audio_bytes = audio_chunk.tobytes()
                
                # Process with enhanced VAD
                vad_result = vad_handler.process_frame(audio_bytes, sample_rate)
                
                frame_count += 1
                
                if vad_result == "INTERRUPT_START":
                    print("🛑 INTERRUPT DETECTED!")
                    print(f"   Frame: {frame_count}")
                    print(f"   Time: {frame_count * chunk_duration:.1f}s")
                    interrupt_detected = True
                    
                    # Continue monitoring for interrupt end
                    print("   Waiting for speech to end...")
                    
                    while True:
                        audio_chunk = sd.rec(chunk_size, samplerate=sample_rate, channels=1, dtype='int16')
                        sd.wait()
                        audio_bytes = audio_chunk.tobytes()
                        
                        vad_result = vad_handler.process_frame(audio_bytes, sample_rate)
                        
                        if vad_result == "INTERRUPT_END":
                            print("✅ INTERRUPT ENDED!")
                            break
                        elif vad_result == "NO_CHANGE":
                            print("   ... still monitoring")
                        
                        await asyncio.sleep(0.05)
                    
                    break
                
                elif vad_result == "NO_CHANGE":
                    # Show periodic status
                    if frame_count % 50 == 0:  # Every 5 seconds
                        print(f"   Monitoring... ({frame_count * chunk_duration:.1f}s)")
                
                await asyncio.sleep(0.05)
                
            except KeyboardInterrupt:
                print("\n⏹️  Monitoring stopped by user")
                break
            except Exception as e:
                print(f"❌ Error during monitoring: {e}")
                break
        
        print("\n📊 Test Results:")
        if interrupt_detected:
            print("✅ Enhanced VAD interrupt detection: WORKING")
            print("✅ Real-time microphone monitoring: WORKING")
            print("✅ Consensus-based detection: WORKING")
        else:
            print("⚠️  No interrupts detected during test")
        
        return interrupt_detected
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_simulated_tts_with_interrupt():
    """Test simulated TTS playback with interrupt monitoring."""
    print("\n🔊 Testing Simulated TTS with Interrupt Monitoring")
    print("=" * 60)
    
    try:
        from utils.audio_utils import RobustVADInterruptHandler
        from core.config.interrupt_config import get_interrupt_config
        import sounddevice as sd
        import numpy as np
        import pygame
        
        # Initialize
        interrupt_config = get_interrupt_config()
        vad_handler = RobustVADInterruptHandler(interrupt_config)
        
        # Simulate TTS playback
        print("🎵 Simulating TTS playback...")
        print("   (In real implementation, this would be actual TTS audio)")
        print("   Speak into microphone to interrupt!")
        print()
        
        # Audio parameters
        sample_rate = 16000
        chunk_duration = 0.1
        chunk_size = int(sample_rate * chunk_duration)
        
        # Simulate 10 seconds of TTS playback
        playback_duration = 10.0
        start_time = time.time()
        interrupt_detected = False
        
        while (time.time() - start_time) < playback_duration and not interrupt_detected:
            try:
                # Record audio chunk
                audio_chunk = sd.rec(chunk_size, samplerate=sample_rate, channels=1, dtype='int16')
                sd.wait()
                audio_bytes = audio_chunk.tobytes()
                
                # Process with enhanced VAD
                vad_result = vad_handler.process_frame(audio_bytes, sample_rate)
                
                if vad_result == "INTERRUPT_START":
                    elapsed = time.time() - start_time
                    print(f"🛑 TTS INTERRUPTED after {elapsed:.1f}s!")
                    print("   In real implementation:")
                    print("   - TTS audio would stop immediately")
                    print("   - User speech would be captured")
                    print("   - STT would process the speech")
                    print("   - New response would be generated")
                    interrupt_detected = True
                    break
                
                # Show progress
                elapsed = time.time() - start_time
                if int(elapsed) % 2 == 0 and elapsed % 1 < 0.1:  # Every 2 seconds
                    remaining = playback_duration - elapsed
                    print(f"   TTS playing... {remaining:.1f}s remaining")
                
                await asyncio.sleep(0.05)
                
            except KeyboardInterrupt:
                print("\n⏹️  Test stopped by user")
                break
            except Exception as e:
                print(f"❌ Error during TTS simulation: {e}")
                break
        
        if not interrupt_detected:
            print("✅ TTS playback completed without interruption")
        
        print("\n📊 TTS Interrupt Test Results:")
        print(f"✅ Simulated TTS duration: {playback_duration}s")
        print(f"✅ Interrupt detection: {'WORKING' if interrupt_detected else 'NOT TRIGGERED'}")
        print(f"✅ Real-time monitoring: WORKING")
        
        return interrupt_detected
        
    except Exception as e:
        print(f"❌ TTS interrupt test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run comprehensive interrupt integration tests."""
    print("🚀 Enhanced VAD Interrupt Integration Test")
    print("=" * 60)
    print()
    
    # Test 1: Real-time interrupt detection
    test1_result = await test_real_time_interrupt_detection()
    
    # Test 2: Simulated TTS with interrupt
    test2_result = await test_simulated_tts_with_interrupt()
    
    # Summary
    print("\n🎉 FINAL TEST SUMMARY")
    print("=" * 60)
    print(f"✅ Real-time interrupt detection: {'PASS' if test1_result else 'FAIL'}")
    print(f"✅ TTS interrupt simulation: {'PASS' if test2_result else 'FAIL'}")
    
    if test1_result or test2_result:
        print("\n🎯 CONCLUSION: Enhanced VAD interrupt system is WORKING!")
        print("\n💡 Next Steps:")
        print("   1. The enhanced VAD system is properly integrated")
        print("   2. Real-time interrupt detection is functional")
        print("   3. The system should work in your voice agent")
        print("   4. If interrupts still don't work in production:")
        print("      - Check if interrupt monitoring is enabled")
        print("      - Verify microphone permissions")
        print("      - Check audio device configuration")
        print("      - Review TTS interrupt monitor integration")
    else:
        print("\n⚠️  CONCLUSION: No interrupts detected during testing")
        print("\n🔧 Troubleshooting:")
        print("   1. Check microphone is working")
        print("   2. Speak louder or closer to microphone")
        print("   3. Check audio device permissions")
        print("   4. Verify VAD configuration")

if __name__ == "__main__":
    print("🎯 Starting Enhanced VAD Interrupt Integration Test...")
    print()
    
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⏹️  Test interrupted by user")
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n✨ Test completed!")
