import asyncio
import os
import sys
from dotenv import load_dotenv

# Add project root to path
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(project_root)

from core.session_manager_v2 import SessionManagerV2
from utils.audio_utils import detect_voice_activity
from utils.audio_utils import record_microphone_audio_vad

load_dotenv()

# 🔧 ENHANCED PROFESSIONAL INTERRUPT CONFIGURATION
# These settings implement best practices from research for robust VAD

# PRODUCTION-READY interrupt sensitivity with consensus-based detection
os.environ.setdefault('VAD_THRESHOLD', '0.5')  # Balanced threshold for production use
os.environ.setdefault('VAD_METHOD', 'silero_vad')  # Use Silero VAD (enterprise-grade)
os.environ.setdefault('WEBRTC_AGGRESSIVENESS', '2')  # Balanced aggressiveness (fallback)
os.environ.setdefault('TTS_INTERRUPT_COOLDOWN_SECONDS', '2.0')  # Reasonable cooldown
os.environ.setdefault('CONSENSUS_FRAMES_REQUIRED', '3')  # Consensus-based detection
os.environ.setdefault('SILENCE_FRAMES_REQUIRED', '8')  # Silence confirmation frames
os.environ.setdefault('USER_SPEECH_END_SILENCE_SECONDS', '2.0')  # Optimized silence detection
os.environ.setdefault('MIN_INTERRUPT_DURATION_SECONDS', '1.0')  # Minimum speech duration
os.environ.setdefault('CONFIRMATION_WINDOW_SECONDS', '2.0')  # Confirmation window
os.environ.setdefault('IMMEDIATE_INTERRUPT_MODE', 'true')  # Enable immediate audio stopping
os.environ.setdefault('VAD_DEBUG_MODE', 'true')  # Enable debug logging
os.environ.setdefault('FREQUENCY_FILTERING', 'true')  # Enable noise filtering
os.environ.setdefault('ADAPTIVE_THRESHOLD', 'true')  # Enable adaptive thresholding
os.environ.setdefault('NOISE_FLOOR_ADAPTATION', 'true')  # Enable noise floor adaptation

print("🎯 ENHANCED PRODUCTION-READY Interrupt Settings Applied:")
print(f"   VAD Method: {os.environ.get('VAD_METHOD')} (Silero VAD = enterprise-grade)")
print(f"   VAD Threshold: {os.environ.get('VAD_THRESHOLD')} (0.5 = balanced threshold)")
print(f"   Consensus Frames: {os.environ.get('CONSENSUS_FRAMES_REQUIRED')} (3 = consensus-based detection)")
print(f"   Silence Frames: {os.environ.get('SILENCE_FRAMES_REQUIRED')} (8 = robust silence detection)")
print(f"   WebRTC Fallback: {os.environ.get('WEBRTC_AGGRESSIVENESS')} (2 = balanced aggressiveness)")
print(f"   Cooldown: {os.environ.get('TTS_INTERRUPT_COOLDOWN_SECONDS')}s (2s = optimized)")
print(f"   🚀 IMMEDIATE INTERRUPT: {os.environ.get('IMMEDIATE_INTERRUPT_MODE')} (stops audio instantly)")
print(f"   🔍 DEBUG MODE: {os.environ.get('VAD_DEBUG_MODE')} (shows energy values)")
print(f"   🎛️  FREQUENCY FILTERING: {os.environ.get('FREQUENCY_FILTERING')} (noise reduction)")
print(f"   📊 ADAPTIVE THRESHOLD: {os.environ.get('ADAPTIVE_THRESHOLD')} (noise adaptation)")

# Verify professional interrupt settings
def verify_professional_settings():
    """Verify that professional interrupt settings are applied."""
    settings = {
        'VAD_THRESHOLD': float(os.getenv('VAD_THRESHOLD', '0.05')),
        'WEBRTC_AGGRESSIVENESS': int(os.getenv('WEBRTC_AGGRESSIVENESS', '3')),
        'REQUIRED_CONSECUTIVE_FRAMES': int(os.getenv('REQUIRED_CONSECUTIVE_FRAMES', '5')),
        'TTS_INTERRUPT_COOLDOWN_SECONDS': float(os.getenv('TTS_INTERRUPT_COOLDOWN_SECONDS', '0.0'))
    }

    print("\n✅ Professional Interrupt Settings Verified:")
    print(f"   🎯 VAD Threshold: {settings['VAD_THRESHOLD']} (higher = less sensitive)")
    print(f"   🎯 WebRTC Aggressiveness: {settings['WEBRTC_AGGRESSIVENESS']} (0 = most conservative)")
    print(f"   🎯 Required Frames: {settings['REQUIRED_CONSECUTIVE_FRAMES']} (more = better validation)")
    print(f"   🎯 Cooldown: {settings['TTS_INTERRUPT_COOLDOWN_SECONDS']}s (prevents rapid re-triggering)")
    print("   🎯 TTS Isolation: 1.5s (prevents TTS feedback)")

    return settings

verify_professional_settings()

async def select_input_device():
    """Select microphone input device for recording."""
    import sounddevice as sd
    devices = sd.query_devices()
    input_devices = [(i, d) for i, d in enumerate(devices) if d['max_input_channels'] > 0]
    print("Available input devices:")
    for idx, dev in input_devices:
        print(f"  [{idx}] {dev['name']} (inputs: {dev['max_input_channels']})")
    while True:
        try:
            device_index = int(input("Enter the device index for your microphone: ").strip())
            if any(idx == device_index for idx, _ in input_devices):
                return device_index
            else:
                print("Invalid index. Please select from the list above.")
        except Exception:
            print("Please enter a valid integer index.")

# async def record_microphone_audio(duration_sec=5, sample_rate=16000, device_index=None):
#     """Record audio from microphone using VAD for voice activity detection."""
#     import sounddevice as sd
#     import wave
#     import time

#     print(f"🎤 [MIC] Recording {duration_sec} seconds of audio...")
#     print("🗣️  Speak now!")

#     # Record audio from microphone
#     audio = sd.rec(int(duration_sec * sample_rate), samplerate=sample_rate, channels=1, dtype='int16', device=device_index)
#     sd.wait()

#     # Create timestamped filename
#     timestamp = int(time.time())
#     audio_filename = f'recorded_audio_{timestamp}.wav'

#     # Save audio to WAV file
#     audio_bytes = audio.tobytes()
#     with wave.open(audio_filename, 'wb') as wf:
#         wf.setnchannels(1)
#         wf.setsampwidth(2)
#         wf.setframerate(sample_rate)
#         wf.writeframes(audio_bytes)

#     # Use VAD to check if voice was detected
#     vad_result = detect_voice_activity(audio_bytes)
#     has_voice = vad_result.outputs.get("has_voice", False)

#     print(f"✅ [INFO] Saved recorded audio to: {audio_filename}")
#     print(f"🔍 [VAD] Voice detected: {has_voice}")

#     return audio_filename

async def get_audio_input_choice():
    """Get user choice for audio input method."""
    print("\n🎯 Audio Input Options:")
    print("1. Use microphone input (live recording)")
    print("2. Use specified audio file path")

    while True:
        try:
            choice = input("Enter your choice (1 or 2): ").strip()
            if choice in ['1', '2']:
                return choice
            else:
                print("Invalid choice. Please enter 1 or 2.")
        except Exception:
            print("Please enter a valid choice (1 or 2).")

async def run_streamlined_voice_workflow():
    """
    Run a streamlined single-flow voice workflow test with two audio input options:
    1. Microphone input (live recording)
    2. Specified audio file path

    Flow: Greeting TTS → User speaks → Processing → Response TTS → End
    """
    print("🎯 Streamlined Voice Workflow Test")
    print("=" * 50)

    # Get audio input choice
    choice = await get_audio_input_choice()

    audio_path = None
    device_index = None

    if choice == "1":
        # Option 1: Microphone input
        device_index = await select_input_device()
        print("✅ Microphone input selected")
    else:
        # Option 2: Audio file path
        while True:
            file_path = input("Enter path to audio file (.wav or .mp3): ").strip()
            if os.path.exists(file_path):
                audio_path = file_path
                print(f"✅ Audio file selected: {audio_path}")
                break
            else:
                print("❌ File not found. Please enter a valid path.")

    # Initialize SessionManagerV2
    session_manager = SessionManagerV2()
    workflow_name = 'banking_workflow_v2.json'
    user_id = 'streamlined_voice_user'

    try:
        # Create a new session
        session_id = await session_manager.create_session(workflow_name, user_id)
        print(f"✅ Session created: {session_id}")

        # Retrieve the memory manager for the session
        memory_manager = session_manager.active_sessions[session_id]["memory_manager"]

        # Initialize orchestrator
        orchestrator = await session_manager.initialize_orchestrator(session_id)

        print("\n🎭 Starting streamlined voice workflow...")
        print("📋 Flow: Greeting TTS → User speaks → Processing → Response TTS → End")
        print()

        # Step 1: Greeting State - TTS only (no audio override)
        # print("🤖 [STEP 1] Playing greeting...")
        # print("   ⏳ Please wait for greeting to complete before speaking")
        # result = await orchestrator.run()
        # print(f"✅ [STEP 2] Greeting completed: {result.get('status', 'unknown')}")

        # Step 2: User Input - After greeting completes
        print("\n🎤 [STEP 1] User input phase...")

        if choice == "1":
            # Record from microphone
            print("   🗣️  Speak now (after greeting finished)...")
            user_audio_path = await record_microphone_audio_vad(device_index=device_index)
        else:
            # Use provided audio file
            user_audio_path = audio_path
            print(f"   📁 Using audio file: {user_audio_path}")

        # Clear any existing audio_path and set user's recorded audio
        # print(f"[DEBUG] Clearing any existing audio_path from memory")
        await memory_manager.delete("user_input_audio_path")
        # audiopath = await memory_manager.get("user_input_audio_path")
        # print(f"[DEBUG] audio_path after delete: {audiopath}")
        # print(f"[DEBUG] Setting user audio_path in memory to: {user_audio_path}")
        await memory_manager.set("contextual", "user_input_audio_path", user_audio_path)
        # audiopath1 = await memory_manager.get("user_input_audio_path")
        # print(f"[DEBUG] audio_path after updateing: {audiopath1}")


        print("🤖 [STEP 1] Playing greeting...")
        print("   ⏳ Please wait for greeting to complete before speaking")
        result = await orchestrator.run()
        print(f"✅ [STEP 2] Greeting completed: {result.get('status', 'unknown')}")


        # Step 3: Processing - STT → Intent → Agent → TTS
        print("\n🤖 [STEP 3] Processing user request...")
        print("   📝 STT: Converting speech to text...")
        print("   🧠 Intent: Analyzing request...")
        print("   ⚙️  Agent: Processing request...")
        print("   🔊 TTS: Generating response...")

        audiopath2 = await memory_manager.get("user_input_audio_path")
        print(f"[DEBUG] audio_path after after run: {audiopath2}")
        # Step 4: Results
        print(f"\n✅ [STEP 3] Workflow completed!")
        print(f"   Status: {result.get('status', 'unknown')}")
        if result.get('reason'):
            print(f"   Details: {result.get('reason')}")

        print("\n🎉 Single-flow test completed successfully!")

    except Exception as e:
        print(f"❌ Error in workflow: {e}")
        import traceback
        traceback.print_exc()

    finally:

        # Clean up the session
        await session_manager.cleanup_session(session_id, reason="streamlined_test_complete")
        print("✅ Session cleaned up. Test complete!")

async def update_orchestrator_for_audio_retrieval():
    """
    Update the orchestrator to retrieve audio path from memory manager.
    This demonstrates the integration pattern you requested.
    """
    print("\n📋 Orchestrator Integration Pattern:")
    print("   The orchestrator should retrieve audio path using:")
    print("   audio_path = await self.memory_manager.get('audio_path')")
    print("   print(f'[DEBUG] STT step - checking for audio. self.memory_manager.get(\"audio_path\")')")
    print("   if audio_path:")
    print("       input_data = {'audio_path': audio_path}")
    print()
    print("   This pattern ensures the orchestrator gets audio from memory")
    print("   instead of hardcoded paths, enabling flexible audio input.")
    print()

if __name__ == "__main__":
    import sys
    import os
    sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..')))
    print("🚀 Starting Streamlined Voice Workflow Test")
    print("This test demonstrates a simplified single-flow voice pipeline with:")
    print("  ✅ Two audio input options (microphone or file)")
    print("  ✅ Complete STT → Intent → Agent → TTS workflow")
    print("  ✅ VAD integration for voice activity detection")
    print("  ✅ Memory manager audio path retrieval")
    print("  ✅ Proper greeting/ending state handling")
    print()

    # Show orchestrator integration pattern
    asyncio.run(update_orchestrator_for_audio_retrieval())

    # Run the streamlined test
    asyncio.run(run_streamlined_voice_workflow())