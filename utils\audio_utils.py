"""
Audio utilities for the Voice Agents Platform.

This module provides audio processing utilities including format conversion,
voice activity detection, audio quality analysis, and audio preprocessing
with integrated structured logging.
"""
import sounddevice as sd

import sys
from pathlib import Path
from typing import Dict, Any, Optional, List, Tuple
import io
import wave
import struct
import math
import os
import asyncio
import threading
import time
from datetime import datetime
from elevenlabs.client import ElevenLabs
import numpy as np
try:
    import webrtcvad
    _webrtcvad_available = True
except ImportError:
    _webrtcvad_available = False
try:
    import pyaudio
    _pyaudio_available = True
except ImportError:
    _pyaudio_available = False
try:
    from silero_vad import load_silero_vad, get_speech_timestamps, read_audio
    _silero_vad_available = True
    _silero_model = None
except ImportError:
    _silero_vad_available = False
    _silero_model = None
try:
    import pyAudioAnalysis
    _pyaudioanalysis_available = True
except ImportError:
    _pyaudioanalysis_available = False
from scipy.signal import butter, lfilter

# Add project root to path for imports
project_root = str(Path(__file__).parent.parent)
sys.path.append(project_root)

from core.logging.logger_config import get_module_logger
from schemas.outputSchema import StateOutput, StatusType, StatusCode
from core.config.interrupt_config import get_interrupt_config
interrupt_config = get_interrupt_config()

# Module logger
logger = get_module_logger("audio_utils")

# VAD Debug mode from environment variable
VAD_DEBUG_MODE = os.getenv("VAD_DEBUG_MODE", "false").lower() == "true"

# --- VAD method toggle ---
# VAD method is now dynamically selected from configuration
# Options: 'webrtcvad', 'pyaudioanalysis', 'energy'
# webrtcvad is more robust against TTS audio feedback than energy-based detection


class AudioProcessor:
    """Audio processing utilities with logging."""
    
    def __init__(self, interrupt_config=None):
        self.supported_formats = ['wav', 'mp3', 'flac', 'ogg']
        self.sample_rates = [8000, 16000, 22050, 44100, 48000]
        self.interrupt_config = interrupt_config

        # Enhanced VAD state tracking
        self._silero_model = None
        self._noise_floor = None
        self._speech_frame_count = 0
        self._silence_frame_count = 0
        self._is_speaking = False

        logger.info(
            "Initialized AudioProcessor",
            action="initialize",
            output_data={
                "supported_formats": self.supported_formats,
                "supported_sample_rates": self.sample_rates
            },
            layer="audio_utils"
        )
    
    def analyze_audio_properties(self, audio_data: bytes, session_id: str = None) -> StateOutput:
        """
        Analyze audio properties like format, duration, sample rate, etc.
        
        Args:
            audio_data: Raw audio data
            session_id: Optional session ID for context
            
        Returns:
            StateOutput with audio properties
        """
        try:
            logger.info(
                "Starting audio analysis",
                action="analyze_audio",
                input_data={"audio_size_bytes": len(audio_data)},
                layer="audio_utils",
                session_id=session_id
            )
            
            if not audio_data:
                return StateOutput(
                    status=StatusType.ERROR,
                    message="Empty audio data provided",
                    code=StatusCode.BAD_REQUEST,
                    outputs={},
                    meta={"error": "empty_audio"}
                )
            
            # Try to parse as WAV file
            try:
                audio_io = io.BytesIO(audio_data)
                with wave.open(audio_io, 'rb') as wav_file:
                    properties = {
                        "format": "wav",
                        "channels": wav_file.getnchannels(),
                        "sample_rate": wav_file.getframerate(),
                        "sample_width": wav_file.getsampwidth(),
                        "frames": wav_file.getnframes(),
                        "duration_seconds": wav_file.getnframes() / wav_file.getframerate(),
                        "size_bytes": len(audio_data)
                    }
                    
                    # Calculate additional metrics
                    properties["bitrate"] = properties["sample_rate"] * properties["sample_width"] * 8 * properties["channels"]
                    properties["is_stereo"] = properties["channels"] == 2
                    properties["is_mono"] = properties["channels"] == 1
                    
            except wave.Error:
                # If not WAV, provide basic analysis
                properties = {
                    "format": "unknown",
                    "size_bytes": len(audio_data),
                    "estimated_duration": len(audio_data) / 32000,  # Rough estimate
                    "analysis_note": "Could not parse as WAV file"
                }
            
            logger.info(
                "Audio analysis completed",
                action="analyze_audio",
                output_data=properties,
                layer="audio_utils",
                session_id=session_id
            )
            
            return StateOutput(
                status=StatusType.SUCCESS,
                message="Audio analysis completed",
                code=StatusCode.OK,
                outputs=properties,
                meta={"format": properties.get("format", "unknown")}
            )
            
        except Exception as e:
            logger.error(
                "Error in audio analysis",
                action="analyze_audio",
                input_data={"audio_size_bytes": len(audio_data) if audio_data else 0},
                reason=str(e),
                layer="audio_utils",
                session_id=session_id
            )
            
            return StateOutput(
                status=StatusType.ERROR,
                message=f"Audio analysis failed: {str(e)}",
                code=StatusCode.INTERNAL_ERROR,
                outputs={},
                meta={"error": str(e)}
            )
    
    def bandpass_filter(self, data, sample_rate, lowcut=300.0, highcut=3400.0, order=5):
        """Apply a bandpass filter to focus on human speech frequencies."""
        nyq = 0.5 * sample_rate
        low = lowcut / nyq
        high = highcut / nyq
        b, a = butter(order, [low, high], btype='band')
        y = lfilter(b, a, data)
        return y


class RobustVADInterruptHandler:
    """
    Commercial-grade VAD interrupt handler implementing best practices from research.
    Features consensus-based detection, adaptive thresholding, and noise filtering.
    """

    def __init__(self, interrupt_config=None):
        self.interrupt_config = interrupt_config or get_interrupt_config()
        self.logger = get_module_logger("RobustVADInterruptHandler")

        # Get configuration values
        self.speech_frames_required = getattr(self.interrupt_config.global_settings, 'consensus_frames_required', 3)
        self.silence_frames_required = getattr(self.interrupt_config.global_settings, 'silence_frames_required', 8)
        self.vad_threshold = getattr(self.interrupt_config.global_settings, 'vad_threshold', 0.5)
        self.vad_method = getattr(self.interrupt_config.global_settings, 'vad_method', 'webrtcvad')
        self.webrtc_aggressiveness = getattr(self.interrupt_config.global_settings, 'webrtc_aggressiveness', 2)

        # State tracking
        self.current_speech_count = 0
        self.current_silence_count = 0
        self.is_speaking = False

        # Initialize VAD processors
        self.audio_processor = AudioProcessor(interrupt_config)

        # Initialize WebRTC VAD as fallback
        if _webrtcvad_available:
            self.webrtc_vad = webrtcvad.Vad(self.webrtc_aggressiveness)
        else:
            self.webrtc_vad = None

        self.logger.info(
            "RobustVADInterruptHandler initialized",
            action="__init__",
            output_data={
                "speech_frames_required": self.speech_frames_required,
                "silence_frames_required": self.silence_frames_required,
                "vad_method": self.vad_method,
                "webrtc_aggressiveness": self.webrtc_aggressiveness
            },
            layer="robust_vad_interrupt_handler"
        )

    def process_frame(self, audio_frame: bytes, sample_rate: int = 16000) -> str:
        """
        Process audio frame and return interrupt decision.

        Returns:
            "INTERRUPT_START": Start interrupt detected
            "INTERRUPT_END": End interrupt detected
            "NO_CHANGE": No state change
        """
        try:
            # Preprocess audio if filtering is enabled
            if getattr(self.interrupt_config.global_settings, 'frequency_filtering', False):
                audio_frame = self.audio_processor.preprocess_audio_for_vad(audio_frame, sample_rate)

            # Calculate adaptive threshold if enabled
            threshold = self.vad_threshold
            if getattr(self.interrupt_config.global_settings, 'adaptive_threshold', False):
                threshold = self.audio_processor.calculate_adaptive_threshold(audio_frame, self.vad_threshold)

            # Detect speech using configured VAD method
            is_speech = self._detect_speech_frame(audio_frame, sample_rate, threshold)

            if is_speech:
                self.current_speech_count += 1
                self.current_silence_count = 0

                # Trigger interrupt only after consistent speech detection
                if (self.current_speech_count >= self.speech_frames_required
                    and not self.is_speaking):
                    self.is_speaking = True
                    self.logger.info(
                        "Interrupt START detected",
                        action="process_frame",
                        output_data={
                            "speech_frames": self.current_speech_count,
                            "required_frames": self.speech_frames_required,
                            "threshold": threshold
                        },
                        layer="robust_vad_interrupt_handler"
                    )
                    return "INTERRUPT_START"

            else:
                self.current_silence_count += 1
                self.current_speech_count = 0

                # End interrupt only after consistent silence
                if (self.current_silence_count >= self.silence_frames_required
                    and self.is_speaking):
                    self.is_speaking = False
                    self.logger.info(
                        "Interrupt END detected",
                        action="process_frame",
                        output_data={
                            "silence_frames": self.current_silence_count,
                            "required_frames": self.silence_frames_required
                        },
                        layer="robust_vad_interrupt_handler"
                    )
                    return "INTERRUPT_END"

            return "NO_CHANGE"

        except Exception as e:
            self.logger.error(
                "Error processing frame",
                action="process_frame",
                reason=str(e),
                layer="robust_vad_interrupt_handler"
            )
            return "NO_CHANGE"

    def _detect_speech_frame(self, audio_frame: bytes, sample_rate: int, threshold: float) -> bool:
        """Detect speech in a single audio frame using the configured VAD method."""
        try:
            if self.vad_method.lower() == 'silero_vad' and _silero_vad_available:
                result = self.audio_processor.vad_with_silero(audio_frame, sample_rate, threshold)
                if result is not None:
                    return result
                # Fall back to WebRTC if Silero fails

            if self.webrtc_vad and len(audio_frame) >= 640:  # Minimum frame size for WebRTC VAD
                # WebRTC VAD requires specific frame sizes (10ms, 20ms, 30ms)
                frame_duration = 30  # ms
                frame_size = int(sample_rate * frame_duration / 1000) * 2  # 2 bytes per sample

                if len(audio_frame) >= frame_size:
                    frame = audio_frame[:frame_size]
                    webrtc_result = self.webrtc_vad.is_speech(frame, sample_rate)

                    # Apply energy threshold check
                    audio_array = np.frombuffer(frame, dtype=np.int16)
                    normalized_audio = audio_array.astype(np.float32) / 32767.0
                    audio_energy = float(np.mean(normalized_audio ** 2))

                    # Combine WebRTC result with energy threshold
                    return webrtc_result and audio_energy >= threshold

            # Fallback to energy-based detection
            audio_array = np.frombuffer(audio_frame, dtype=np.int16)
            normalized_audio = audio_array.astype(np.float32) / 32767.0
            audio_energy = float(np.mean(normalized_audio ** 2))

            return audio_energy >= threshold

        except Exception as e:
            self.logger.warning(f"Speech detection failed: {e}", action="_detect_speech_frame", layer="robust_vad_interrupt_handler")
            return False

    def reset_state(self):
        """Reset the interrupt handler state."""
        self.current_speech_count = 0
        self.current_silence_count = 0
        self.is_speaking = False
        self.logger.info("VAD interrupt handler state reset", action="reset_state", layer="robust_vad_interrupt_handler")


    def preprocess_audio_for_vad(self, audio_data: bytes, sample_rate: int = 16000) -> bytes:
        """Apply frequency filtering to reduce non-speech noise."""
        try:
            if not hasattr(self.interrupt_config, 'global_settings') or not getattr(self.interrupt_config.global_settings, 'frequency_filtering', False):
                return audio_data

            # Convert to numpy array
            audio_array = np.frombuffer(audio_data, dtype=np.int16)

            # Get filter settings from config
            high_pass_cutoff = 80
            low_pass_cutoff = 8000
            filter_order = 4

            if hasattr(self.interrupt_config, 'advanced_settings'):
                freq_filter = getattr(self.interrupt_config.advanced_settings, 'frequency_filter', {})
                high_pass_cutoff = freq_filter.get('high_pass_cutoff', 80)
                low_pass_cutoff = freq_filter.get('low_pass_cutoff', 8000)
                filter_order = freq_filter.get('filter_order', 4)

            # Apply high-pass filter to remove low-frequency noise (< 80Hz)
            from scipy.signal import butter, sosfilt
            sos_high = butter(filter_order, high_pass_cutoff, btype='high', fs=sample_rate, output='sos')
            audio_filtered = sosfilt(sos_high, audio_array.astype(np.float32))

            # Apply low-pass filter to remove high-frequency noise (> 8000Hz)
            sos_low = butter(filter_order, low_pass_cutoff, btype='low', fs=sample_rate, output='sos')
            audio_filtered = sosfilt(sos_low, audio_filtered)

            # Convert back to int16 and bytes
            audio_filtered = np.clip(audio_filtered, -32767, 32767).astype(np.int16)
            return audio_filtered.tobytes()

        except Exception as e:
            logger.warning(f"Audio preprocessing failed, using original audio: {e}", action="preprocess_audio_for_vad", layer="audio_utils")
            return audio_data

    def calculate_adaptive_threshold(self, audio_data: bytes, base_threshold: float = 0.5) -> float:
        """Adjust VAD threshold based on background noise level."""
        try:
            if not hasattr(self.interrupt_config, 'global_settings') or not getattr(self.interrupt_config.global_settings, 'adaptive_threshold', False):
                return base_threshold

            # Calculate noise floor from first portion of audio (500ms)
            sample_rate = 16000
            noise_sample_size = int(sample_rate * 0.5 * 2)  # 500ms * 2 bytes per sample
            noise_sample = audio_data[:min(noise_sample_size, len(audio_data))]

            if len(noise_sample) < 1000:  # Too short for reliable noise estimation
                return base_threshold

            # Convert to numpy and calculate noise level
            noise_array = np.frombuffer(noise_sample, dtype=np.int16)
            noise_level = np.std(noise_array.astype(np.float32) / 32767.0)

            # Get adaptation settings from config
            high_noise_threshold = 0.02
            medium_noise_threshold = 0.01
            threshold_adjustment_high = 0.2
            threshold_adjustment_medium = 0.1

            if hasattr(self.interrupt_config, 'advanced_settings'):
                noise_adapt = getattr(self.interrupt_config.advanced_settings, 'noise_adaptation', {})
                high_noise_threshold = noise_adapt.get('high_noise_threshold', 0.02)
                medium_noise_threshold = noise_adapt.get('medium_noise_threshold', 0.01)
                threshold_adjustment_high = noise_adapt.get('threshold_adjustment_high', 0.2)
                threshold_adjustment_medium = noise_adapt.get('threshold_adjustment_medium', 0.1)

            # Adjust threshold based on noise level
            if noise_level > high_noise_threshold:  # High noise environment
                adjusted_threshold = base_threshold + threshold_adjustment_high
            elif noise_level > medium_noise_threshold:  # Medium noise
                adjusted_threshold = base_threshold + threshold_adjustment_medium
            else:  # Low noise
                adjusted_threshold = base_threshold

            # Cap at 0.9 to prevent over-sensitivity
            adjusted_threshold = min(adjusted_threshold, 0.9)

            if VAD_DEBUG_MODE:
                logger.info(
                    f"Adaptive threshold: {base_threshold} -> {adjusted_threshold} (noise_level: {noise_level:.4f})",
                    action="calculate_adaptive_threshold",
                    layer="audio_utils"
                )

            return adjusted_threshold

        except Exception as e:
            logger.warning(f"Adaptive threshold calculation failed, using base threshold: {e}", action="calculate_adaptive_threshold", layer="audio_utils")
            return base_threshold

    def _get_silero_model(self):
        """Lazy load Silero VAD model."""
        global _silero_model
        if _silero_model is None and _silero_vad_available:
            try:
                _silero_model = load_silero_vad()
                logger.info("Silero VAD model loaded successfully", action="_get_silero_model", layer="audio_utils")
            except Exception as e:
                logger.error(f"Failed to load Silero VAD model: {e}", action="_get_silero_model", layer="audio_utils")
                return None
        return _silero_model

    def vad_with_silero(self, audio_data: bytes, sample_rate: int = 16000, threshold: float = None) -> Optional[bool]:
        """
        Enhanced Silero VAD implementation with production-ready settings.
        Returns True if speech is detected, False if not, or None if unavailable/error.
        """
        if not _silero_vad_available:
            logger.warning("Silero VAD not available, falling back to WebRTC VAD.")
            return None

        try:
            model = self._get_silero_model()
            if model is None:
                return None

            # Get configuration from interrupt config
            if threshold is None:
                threshold = getattr(self.interrupt_config.global_settings, 'vad_threshold', 0.5) if hasattr(self, 'interrupt_config') and self.interrupt_config else 0.5

            min_speech_duration_ms = getattr(self.interrupt_config.global_settings, 'min_speech_duration_ms', 250) if hasattr(self, 'interrupt_config') and self.interrupt_config else 250
            min_silence_duration_ms = getattr(self.interrupt_config.global_settings, 'min_silence_duration_ms', 100) if hasattr(self, 'interrupt_config') and self.interrupt_config else 100
            speech_pad_ms = getattr(self.interrupt_config.global_settings, 'speech_pad_ms', 30) if hasattr(self, 'interrupt_config') and self.interrupt_config else 30

            # Convert bytes to numpy array
            import tempfile
            import os

            # Save audio data to temporary file for Silero processing
            with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_file:
                import wave
                with wave.open(temp_file.name, 'wb') as wf:
                    wf.setnchannels(1)
                    wf.setsampwidth(2)  # 16-bit
                    wf.setframerate(sample_rate)
                    wf.writeframes(audio_data)

                try:
                    # Use Silero VAD to get speech timestamps
                    wav = read_audio(temp_file.name)
                    speech_timestamps = get_speech_timestamps(
                        wav,
                        model,
                        threshold=threshold,
                        sampling_rate=sample_rate,
                        min_speech_duration_ms=min_speech_duration_ms,
                        min_silence_duration_ms=min_silence_duration_ms,
                        window_size_samples=1536,
                        speech_pad_ms=speech_pad_ms,
                        return_seconds=True
                    )

                    # Check if any speech was detected
                    has_voice = len(speech_timestamps) > 0

                    if VAD_DEBUG_MODE:
                        total_speech_duration = sum(ts['end'] - ts['start'] for ts in speech_timestamps)
                        logger.info(
                            f"Silero VAD result: has_voice={has_voice} "
                            f"(threshold={threshold}, segments={len(speech_timestamps)}, "
                            f"total_speech_duration={total_speech_duration:.2f}s)",
                            action="vad_with_silero",
                            layer="audio_utils"
                        )

                    return has_voice

                finally:
                    # Clean up temporary file
                    try:
                        os.unlink(temp_file.name)
                    except:
                        pass

        except Exception as e:
            logger.error(f"Silero VAD failed: {e}", action="vad_with_silero", layer="audio_utils")
            return None

    def vad_with_webrtcvad(self, audio_data: bytes, sample_rate: int = 16000, aggressiveness: int = None, threshold: float = None) -> Optional[bool]:
        """
        Enhanced webrtcvad implementation with TTS feedback resistance AND threshold support.
        Now properly respects the VAD threshold for professional sensitivity control.
        Returns True if speech is detected, False if not, or None if unavailable/error.
        """
        if not _webrtcvad_available:
            logger.warning("webrtcvad not available, cannot use webrtcvad VAD.")
            return None
        try:
            # Get threshold from config if not provided
            if threshold is None:
                threshold = getattr(self.interrupt_config.global_settings, 'vad_threshold', 0.6) if hasattr(self, 'interrupt_config') and self.interrupt_config else 0.6

            # Use configurable aggressiveness for stricter voice detection
            if aggressiveness is None:
                # Get from interrupt config if available
                aggressiveness = getattr(self.interrupt_config.global_settings, 'webrtc_aggressiveness', 0) if hasattr(self, 'interrupt_config') and self.interrupt_config else 0
            vad = webrtcvad.Vad(aggressiveness)

            # webrtcvad expects 16-bit mono PCM, 10/20/30ms frames
            frame_duration = 30  # ms
            bytes_per_sample = 2
            frame_size = int(sample_rate * frame_duration / 1000) * bytes_per_sample

            # Get required consecutive frames from config
            required_consecutive_frames = getattr(self.interrupt_config.global_settings, 'required_consecutive_frames', 25) if hasattr(self, 'interrupt_config') and self.interrupt_config else 25
            total_frames = 0
            speech_frames = 0
            consecutive_speech_frames = 0

            # Calculate audio energy to respect threshold
            import numpy as np
            audio_array = np.frombuffer(audio_data, dtype=np.int16)

            # PROPER ENERGY CALCULATION: Normalize energy to 0-1 range like other VAD methods
            # Convert to float and normalize by max possible value (32767 for int16)
            normalized_audio = audio_array.astype(np.float32) / 32767.0
            audio_energy = float(np.mean(normalized_audio ** 2))

            # CRITICAL FIX: Respect the threshold! If energy is below threshold, return False immediately
            if audio_energy < threshold:
                if VAD_DEBUG_MODE:
                    logger.info(
                        f"webrtcvad VAD result: has_voice=False (normalized_energy {audio_energy:.6f} < threshold {threshold})",
                        action="vad_with_webrtcvad",
                        layer="audio_utils"
                    )
                return False

            for i in range(0, len(audio_data) - frame_size + 1, frame_size):
                frame = audio_data[i:i+frame_size]
                if len(frame) < frame_size:
                    break

                total_frames += 1
                if vad.is_speech(frame, sample_rate):
                    speech_frames += 1
                    consecutive_speech_frames += 1

                    # If we have enough consecutive speech frames, it's likely real speech
                    if consecutive_speech_frames >= required_consecutive_frames:
                        if VAD_DEBUG_MODE:
                            logger.info(
                                f"webrtcvad VAD result: has_voice=True (energy: {audio_energy:.6f}, consecutive frames: {consecutive_speech_frames})",
                                action="vad_with_webrtcvad",
                                layer="audio_utils"
                            )
                        return True
                else:
                    consecutive_speech_frames = 0

            # Calculate speech ratio for additional validation
            speech_ratio = speech_frames / total_frames if total_frames > 0 else 0

            # PROFESSIONAL SETTINGS: Much stricter requirements
            min_speech_ratio = 0.6  # Increased from 0.3 to 0.6 (60% speech frames required)
            min_consecutive = max(5, required_consecutive_frames // 5)  # At least 5 frames

            has_voice = (speech_ratio >= min_speech_ratio and
                        consecutive_speech_frames >= min_consecutive and
                        audio_energy >= threshold)  # CRITICAL: Always check threshold

            if VAD_DEBUG_MODE:
                logger.info(
                    f"webrtcvad VAD result: has_voice={has_voice} "
                    f"(energy={audio_energy:.6f}, threshold={threshold}, speech_ratio={speech_ratio:.2f}, max_consecutive={consecutive_speech_frames})",
                    action="vad_with_webrtcvad",
                    layer="audio_utils"
                )
            return has_voice

        except Exception as e:
            logger.error(f"webrtcvad VAD failed: {e}", action="vad_with_webrtcvad", layer="audio_utils")
            return None

    def detect_voice_activity(self, audio_data: bytes, threshold: float = None, session_id: str = None, use_pyaudioanalysis: bool = None) -> StateOutput:
        """
        Enhanced: Use webrtcvad, pyAudioAnalysis, or energy-based VAD based on VAD_METHOD toggle.
        """
        if self.interrupt_config and not self.interrupt_config.global_settings.enabled:
            return StateOutput(
                status=StatusType.SUCCESS,
                message="Interrupt detection is disabled in config.",
                code=StatusCode.OK,
                outputs={"has_voice": False, "detection_disabled": True},
                meta={"interrupt_detection_enabled": False}
            )
        if threshold is None and self.interrupt_config is not None:
            threshold = self.interrupt_config.global_settings.vad_threshold
        elif threshold is None:
            threshold = 0.6  # FIXED: Professional default threshold (was 0.05)

        # --- VAD method selection from configuration ---
        # Get VAD method from interrupt config, fallback to webrtcvad
        vad_method = self.interrupt_config.global_settings.vad_method.lower() if self.interrupt_config else 'webrtcvad'

        # Try Silero VAD first if configured
        if vad_method == 'silero_vad' and _silero_vad_available:
            logger.info(f"Using Silero VAD with threshold {threshold}", action="detect_voice_activity", layer="audio_utils", session_id=session_id)
            vad_result = self.vad_with_silero(audio_data, threshold=threshold)
            if vad_result is not None:
                return StateOutput(
                    status=StatusType.SUCCESS,
                    message=f"Voice activity: {'detected' if vad_result else 'not detected'} (silero_vad, threshold={threshold})",
                    code=StatusCode.OK,
                    outputs={
                        "has_voice": vad_result,
                        "vad_method": "silero_vad",
                        "threshold_used": threshold
                    },
                    meta={"has_voice": vad_result}
                )
            else:
                logger.warning("Silero VAD failed, falling back to WebRTC VAD.", action="detect_voice_activity", layer="audio_utils", session_id=session_id)
                # Fall through to WebRTC VAD

        if vad_method == 'webrtcvad' and _webrtcvad_available:
            logger.info(f"Using webrtcvad for VAD with threshold {threshold}", action="detect_voice_activity", layer="audio_utils", session_id=session_id)
            vad_result = self.vad_with_webrtcvad(audio_data, threshold=threshold)  # CRITICAL FIX: Pass threshold!
            if vad_result is not None:
                return StateOutput(
                    status=StatusType.SUCCESS,
                    message=f"Voice activity: {'detected' if vad_result else 'not detected'} (webrtcvad, threshold={threshold})",
                    code=StatusCode.OK,
                    outputs={
                        "has_voice": vad_result,
                        "vad_method": "webrtcvad",
                        "threshold_used": threshold
                    },
                    meta={"has_voice": vad_result}
                )
            else:
                logger.warning("webrtcvad VAD failed, falling back to pyAudioAnalysis or energy-based VAD.", action="detect_voice_activity", layer="audio_utils", session_id=session_id)
        if vad_method == 'pyaudioanalysis' and _pyaudioanalysis_available:
            logger.info("Using pyAudioAnalysis for VAD", action="detect_voice_activity", layer="audio_utils", session_id=session_id)
            vad_result = self.vad_with_pyaudioanalysis(audio_data)
            if vad_result is not None:
                return StateOutput(
                    status=StatusType.SUCCESS,
                    message=f"Voice activity: {'detected' if vad_result else 'not detected'} (pyAudioAnalysis)",
                    code=StatusCode.OK,
                    outputs={
                        "has_voice": vad_result,
                        "vad_method": "pyAudioAnalysis"
                    },
                    meta={"has_voice": vad_result}
                )
            else:
                logger.warning("pyAudioAnalysis VAD failed, falling back to energy-based VAD.", action="detect_voice_activity", layer="audio_utils", session_id=session_id)
        # Default: energy-based VAD
        try:
            logger.info(
                "Starting voice activity detection (upgraded)",
                action="detect_voice_activity",
                input_data={"audio_size_bytes": len(audio_data), "threshold": threshold},
                layer="audio_utils",
                session_id=session_id
            )

            if not audio_data:
                return StateOutput(
                    status=StatusType.ERROR,
                    message="Empty audio data provided",
                    code=StatusCode.BAD_REQUEST,
                    outputs={},
                    meta={"error": "empty_audio"}
                )

            # Try to parse as WAV file
            try:
                audio_io = io.BytesIO(audio_data)
                with wave.open(audio_io, 'rb') as wav_file:
                    sample_rate = wav_file.getframerate()
                    sample_width = wav_file.getsampwidth()
                    channels = wav_file.getnchannels()
                    frames = wav_file.readframes(wav_file.getnframes())

                    # Convert to mono if needed
                    if channels > 1:
                        logger.info("Converting multi-channel audio to mono for VAD", action="detect_voice_activity", layer="audio_utils")
                        # Only keep the first channel
                        if sample_width == 2:
                            samples = np.frombuffer(frames, dtype=np.int16)
                            samples = samples[::channels]
                        elif sample_width == 1:
                            samples = np.frombuffer(frames, dtype=np.uint8)
                            samples = samples[::channels]
                        else:
                            raise ValueError(f"Unsupported sample width: {sample_width}")
                    else:
                        if sample_width == 2:
                            samples = np.frombuffer(frames, dtype=np.int16)
                        elif sample_width == 1:
                            samples = np.frombuffer(frames, dtype=np.uint8)
                        else:
                            raise ValueError(f"Unsupported sample width: {sample_width}")

                    # Apply bandpass filter
                    filtered_samples = self.bandpass_filter(samples, sample_rate)
                    filtered_samples = filtered_samples.astype(samples.dtype)

                    # Prepare PCM bytes for webrtcvad (16-bit mono PCM, 8kHz/16kHz/32kHz/48kHz)
                    if sample_width == 2:
                        pcm_bytes = filtered_samples.tobytes()
                    elif sample_width == 1:
                        # Convert 8-bit unsigned to 16-bit signed
                        pcm_bytes = (filtered_samples.astype(np.int16) - 128).tobytes()
                    else:
                        pcm_bytes = filtered_samples.tobytes()

                    # Use improved energy-based VAD with noise floor detection
                    # Calculate energy-based voice detection with adaptive thresholding
                    energy = np.mean(filtered_samples ** 2)

                    # Calculate additional metrics for better detection
                    rms_energy = np.sqrt(energy)
                    peak_amplitude = np.max(np.abs(filtered_samples))
                    zero_crossing_rate = np.mean(np.diff(np.sign(filtered_samples)) != 0)

                    # Adaptive threshold based on signal characteristics
                    # Higher threshold for low zero-crossing rate (likely TTS noise)
                    adaptive_threshold = threshold
                    if zero_crossing_rate < 0.1:  # Very low ZCR suggests TTS noise
                        adaptive_threshold = threshold * 5.0  # Increased from 3.0 to better suppress TTS
                    elif zero_crossing_rate < 0.2:  # Low ZCR suggests possible TTS noise
                        adaptive_threshold = threshold * 3.0  # Increased from 2.0 for better suppression

                    # Require both energy and peak amplitude thresholds
                    energy_check = energy > adaptive_threshold
                    amplitude_check = peak_amplitude > (np.sqrt(adaptive_threshold) * 100)  # Scale for amplitude

                    has_voice = energy_check and amplitude_check
                    voice_ratio = 1.0 if has_voice else 0.0
                    num_windows = 1
                    voice_windows = 1 if has_voice else 0

                    # Debug logging if enabled
                    debug_mode = os.getenv('VAD_DEBUG_MODE', 'false').lower() == 'true'
                    if debug_mode or has_voice:
                        logger.info(
                            f"VAD result (improved): energy={energy:.6f}, adaptive_threshold={adaptive_threshold:.6f}, "
                            f"peak_amp={peak_amplitude:.2f}, zcr={zero_crossing_rate:.3f}, has_voice={has_voice}",
                            action="detect_voice_activity",
                            output_data={
                                "has_voice": has_voice,
                                "energy": energy,
                                "adaptive_threshold": adaptive_threshold,
                                "peak_amplitude": peak_amplitude,
                                "zero_crossing_rate": zero_crossing_rate,
                                "energy_check": energy_check,
                                "amplitude_check": amplitude_check,
                                "voice_ratio": voice_ratio
                            },
                            layer="audio_utils",
                            session_id=session_id
                        )
                    else:
                        logger.debug(
                            "VAD result (energy-based)",
                            action="detect_voice_activity",
                            output_data={"has_voice": has_voice, "voice_ratio": voice_ratio, "num_windows": num_windows},
                            layer="audio_utils",
                            session_id=session_id
                        )
                    return StateOutput(
                        status=StatusType.SUCCESS,
                        message=f"Voice activity: {'detected' if has_voice else 'not detected'} (energy-based)",
                        code=StatusCode.OK,
                        outputs={
                            "has_voice": has_voice,
                            "voice_ratio": voice_ratio,
                            "total_windows": num_windows,
                            "voice_windows": voice_windows,
                            "vad_method": "energy"
                        },
                        meta={"has_voice": has_voice}
                    )
            except wave.Error as e:
                # Suppress expected error for non-WAV input
                logger.info(
                    f"WAV parse failed (expected for raw PCM): {e}",
                    action="detect_voice_activity",
                    layer="audio_utils",
                    session_id=session_id
                )
                # Fallback: simple amplitude/energy analysis
                try:
                    # Try to decode as PCM 16-bit mono
                    samples = np.frombuffer(audio_data, dtype=np.int16)
                    sample_rate = 16000  # Assume 16kHz if unknown
                    filtered_samples = self.bandpass_filter(samples, sample_rate)
                    energy = float(np.mean(filtered_samples ** 2))
                    has_voice = bool(energy > threshold)
                    # Ensure all outputs are native Python types
                    logger.info(
                        "Fallback VAD result (energy-based)",
                        action="detect_voice_activity",
                        output_data={"has_voice": has_voice, "energy": energy},
                        layer="audio_utils",
                        session_id=session_id
                    )
                    return StateOutput(
                        status=StatusType.SUCCESS,
                        message=f"Voice activity: {'detected' if has_voice else 'not detected'} (energy-based)",
                        code=StatusCode.OK,
                        outputs={
                            "has_voice": has_voice,
                            "energy": energy,
                            "vad_method": "energy"
                        },
                        meta={"has_voice": has_voice}
                    )
                except Exception as e2:
                    logger.error(
                        "VAD failed on all methods",
                        action="detect_voice_activity",
                        reason=f"{str(e)} | {str(e2)}",
                        layer="audio_utils",
                        session_id=session_id
                    )
                    return StateOutput(
                        status=StatusType.ERROR,
                        message=f"Voice activity detection failed: {str(e)} | {str(e2)}",
                        code=StatusCode.INTERNAL_ERROR,
                        outputs={},
                        meta={"error": f"{str(e)} | {str(e2)}"}
                    )
        except Exception as e:
            logger.error(
                "Error in voice activity detection (upgraded)",
                action="detect_voice_activity",
                input_data={"audio_size_bytes": len(audio_data) if audio_data else 0},
                reason=str(e),
                layer="audio_utils",
                session_id=session_id
            )
            return StateOutput(
                status=StatusType.ERROR,
                message=f"Voice activity detection failed: {str(e)}",
                code=StatusCode.INTERNAL_ERROR,
                outputs={},
                meta={"error": str(e)}
            )
    
    def validate_audio_quality(self, audio_data: bytes, session_id: str = None) -> StateOutput:
        """
        Validate audio quality and provide recommendations.
        
        Args:
            audio_data: Raw audio data
            session_id: Optional session ID for context
            
        Returns:
            StateOutput with quality assessment
        """
        try:
            logger.info(
                "Starting audio quality validation",
                action="validate_quality",
                input_data={"audio_size_bytes": len(audio_data)},
                layer="audio_utils",
                session_id=session_id
            )
            
            # Get audio properties first
            props_result = self.analyze_audio_properties(audio_data, session_id)
            if props_result.status != StatusType.SUCCESS:
                return props_result
            
            properties = props_result.outputs
            quality_issues = []
            recommendations = []
            quality_score = 100  # Start with perfect score
            
            # Check sample rate
            if properties.get("sample_rate", 0) < 16000:
                quality_issues.append("Low sample rate")
                recommendations.append("Use at least 16kHz sample rate for better quality")
                quality_score -= 20
            
            # Check duration
            duration = properties.get("duration_seconds", 0)
            if duration < 0.5:
                quality_issues.append("Audio too short")
                recommendations.append("Provide at least 0.5 seconds of audio")
                quality_score -= 30
            elif duration > 30:
                quality_issues.append("Audio very long")
                recommendations.append("Consider splitting long audio into smaller chunks")
                quality_score -= 10
            
            # Check channels
            if properties.get("channels", 0) > 2:
                quality_issues.append("Too many channels")
                recommendations.append("Use mono or stereo audio")
                quality_score -= 15
            
            # Check file size
            size_mb = properties.get("size_bytes", 0) / (1024 * 1024)
            if size_mb > 10:
                quality_issues.append("Large file size")
                recommendations.append("Consider compressing audio or reducing quality")
                quality_score -= 10
            
            quality_score = max(quality_score, 0)  # Don't go below 0
            
            result = {
                "quality_score": quality_score,
                "quality_grade": "A" if quality_score >= 90 else "B" if quality_score >= 70 else "C" if quality_score >= 50 else "D",
                "issues": quality_issues,
                "recommendations": recommendations,
                "properties": properties,
                "is_acceptable": quality_score >= 50
            }
            
            logger.info(
                "Audio quality validation completed",
                action="validate_quality",
                output_data={
                    "quality_score": quality_score,
                    "quality_grade": result["quality_grade"],
                    "issues_count": len(quality_issues)
                },
                layer="audio_utils",
                session_id=session_id
            )
            
            return StateOutput(
                status=StatusType.SUCCESS,
                message=f"Audio quality: {result['quality_grade']} (Score: {quality_score})",
                code=StatusCode.OK,
                outputs=result,
                meta={"quality_score": quality_score}
            )
            
        except Exception as e:
            logger.error(
                "Error in audio quality validation",
                action="validate_quality",
                input_data={"audio_size_bytes": len(audio_data) if audio_data else 0},
                reason=str(e),
                layer="audio_utils",
                session_id=session_id
            )
            
            return StateOutput(
                status=StatusType.ERROR,
                message=f"Audio quality validation failed: {str(e)}",
                code=StatusCode.INTERNAL_ERROR,
                outputs={},
                meta={"error": str(e)}
            )


class TTSPlaybackController:
    """
    Controls TTS audio playback with pause/resume functionality and interrupt detection.
    Manages playback state and provides hooks for interrupt handling.
    """

    def __init__(self, session_id: str, interrupt_config=None):
        self.session_id = session_id
        self.logger = get_module_logger(f"TTSPlaybackController", session_id=session_id)
        self.interrupt_config = interrupt_config

        # Playback state
        self.is_playing = False
        self.is_paused = False
        self.current_audio_path = None
        self.playback_start_time = None
        self.pause_time = None
        self.total_pause_duration = 0
        self.audio_duration = None

        # Interrupt detection
        self.interrupt_detected = False
        self.interrupt_callback = None
        if interrupt_config is not None:
            self.vad_threshold = interrupt_config.global_settings.vad_threshold
            self.confirmation_window = interrupt_config.global_settings.confirmation_window_seconds
        else:
            self.vad_threshold = 0.6  # FIXED: Professional default (was 0.01)
            self.confirmation_window = 1.5

        # Threading for playback control
        self.playback_thread = None
        self.stop_playback = threading.Event()
        self.pause_playback_event = threading.Event()

    async def start_playback_with_interrupt_detection(self, audio_path: str, interrupt_callback=None, resume_from_position: float = 0.0) -> StateOutput:
        """
        Start audio playback with concurrent interrupt detection.

        Args:
            audio_path: Path to the audio file to play
            interrupt_callback: Async callback function to call when interrupt is detected
            resume_from_position: Position (in seconds) to start playback from (for partial resume)

        Returns:
            StateOutput with playback status
        """
        try:
            self.logger.info(
                "Starting TTS playback with interrupt detection",
                action="start_playback",
                input_data={"audio_path": audio_path, "resume_from_position": resume_from_position},
                layer="tts_playback_controller"
            )

            if not os.path.exists(audio_path):
                return StateOutput(
                    status=StatusType.ERROR,
                    message=f"Audio file not found: {audio_path}",
                    code=StatusCode.NOT_FOUND,
                    outputs={},
                    meta={"error": "file_not_found"}
                )

            # Get audio duration for tracking
            self.audio_duration = await self._get_audio_duration(audio_path)

            # Reset state
            self.current_audio_path = audio_path
            self.interrupt_callback = interrupt_callback
            self.interrupt_detected = False
            self.is_playing = True
            self.is_paused = False
            self.playback_start_time = time.time() - resume_from_position
            self.total_pause_duration = 0
            self.stop_playback.clear()
            self.pause_playback_event.clear()

            # Start playback in separate thread, pass resume_from_position
            self.playback_thread = threading.Thread(
                target=self._playback_worker,
                args=(audio_path, resume_from_position),
                daemon=True
            )
            self.playback_thread.start()

            # Start interrupt detection
            interrupt_task = asyncio.create_task(self._interrupt_detection_loop())

            return StateOutput(
                status=StatusType.SUCCESS,
                message="TTS playback started with interrupt detection",
                code=StatusCode.OK,
                outputs={
                    "playback_started": True,
                    "audio_path": audio_path,
                    "audio_duration": self.audio_duration,
                    "interrupt_detection_active": True,
                    "resume_from_position": resume_from_position
                },
                meta={"playback_controller": "active"}
            )

        except Exception as e:
            self.logger.error(
                "Error starting TTS playback",
                action="start_playback",
                input_data={"audio_path": audio_path, "resume_from_position": resume_from_position},
                reason=str(e),
                layer="tts_playback_controller"
            )

            return StateOutput(
                status=StatusType.ERROR,
                message=f"Failed to start TTS playback: {str(e)}",
                code=StatusCode.INTERNAL_ERROR,
                outputs={},
                meta={"error": str(e)}
            )

    async def pause_playback(self) -> StateOutput:
        """Pause the current audio playback."""
        try:
            if not self.is_playing or self.is_paused:
                return StateOutput(
                    status=StatusType.ERROR,
                    message="No active playback to pause",
                    code=StatusCode.BAD_REQUEST,
                    outputs={},
                    meta={"error": "no_active_playback"}
                )

            self.pause_playback_event.set()
            self.is_paused = True
            self.pause_time = time.time()

            self.logger.info(
                "TTS playback paused",
                action="pause_playback",
                output_data={"paused_at": self.get_current_playback_position()},
                layer="tts_playback_controller"
            )

            return StateOutput(
                status=StatusType.SUCCESS,
                message="TTS playback paused",
                code=StatusCode.OK,
                outputs={
                    "paused": True,
                    "playback_position": self.get_current_playback_position()
                },
                meta={"playback_state": "paused"}
            )

        except Exception as e:
            self.logger.error(
                "Error pausing TTS playback",
                action="pause_playback",
                reason=str(e),
                layer="tts_playback_controller"
            )

            return StateOutput(
                status=StatusType.ERROR,
                message=f"Failed to pause TTS playback: {str(e)}",
                code=StatusCode.INTERNAL_ERROR,
                outputs={},
                meta={"error": str(e)}
            )

    async def resume_playback(self) -> StateOutput:
        """Resume paused audio playback."""
        try:
            if not self.is_paused:
                return StateOutput(
                    status=StatusType.ERROR,
                    message="No paused playback to resume",
                    code=StatusCode.BAD_REQUEST,
                    outputs={},
                    meta={"error": "no_paused_playback"}
                )

            # Calculate pause duration
            if self.pause_time:
                self.total_pause_duration += time.time() - self.pause_time
                self.pause_time = None

            self.pause_playback_event.clear()
            self.is_paused = False

            self.logger.info(
                "TTS playback resumed",
                action="resume_playback",
                output_data={"resumed_at": self.get_current_playback_position()},
                layer="tts_playback_controller"
            )

            return StateOutput(
                status=StatusType.SUCCESS,
                message="TTS playback resumed",
                code=StatusCode.OK,
                outputs={
                    "resumed": True,
                    "playback_position": self.get_current_playback_position()
                },
                meta={"playback_state": "playing"}
            )

        except Exception as e:
            self.logger.error(
                "Error resuming TTS playback",
                action="resume_playback",
                reason=str(e),
                layer="tts_playback_controller"
            )

            return StateOutput(
                status=StatusType.ERROR,
                message=f"Failed to resume TTS playback: {str(e)}",
                code=StatusCode.INTERNAL_ERROR,
                outputs={},
                meta={"error": str(e)}
            )

    async def resume_playback_from_position(self, audio_path: str, position: float) -> StateOutput:
        """
        Resume TTS playback from a specific position.

        Args:
            audio_path: Path to the audio file to resume
            position: Position (in seconds) to resume from

        Returns:
            StateOutput with resume status
        """
        try:
            self.logger.info(
                "Resuming TTS playback from specific position",
                action="resume_playback_from_position",
                input_data={"audio_path": audio_path, "position": position},
                layer="tts_playback_controller"
            )

            # Play a test beep for verification (cross-platform)
            try:
                import sys
                if sys.platform == "win32":
                    import winsound
                    winsound.Beep(1000, 300)  # 1000 Hz for 300 ms
                else:
                    # Try to use 'beep' command on Unix
                    import os
                    os.system('printf "\a"')
            except Exception:
                pass  # Ignore if beep not available

            # Log the resume event
            print(f"[AUDIO TEST] Resuming playback: {audio_path} from {position:.2f} seconds")

            # Stop current playback if any
            await self.stop_playback()

            # Start playback from the specified position
            return await self.start_playback_with_interrupt_detection(
                audio_path,
                interrupt_callback=None,  # No interrupt detection for resumed playback
                resume_from_position=position
            )

        except Exception as e:
            self.logger.error(
                "Error resuming TTS playback from position",
                action="resume_playback_from_position",
                reason=str(e),
                layer="tts_playback_controller"
            )

            return StateOutput(
                status=StatusType.ERROR,
                message=f"Failed to resume TTS playback from position: {str(e)}",
                code=StatusCode.INTERNAL_ERROR,
                outputs={},
                meta={"error": str(e)}
            )

    async def stop_playback_completely(self) -> StateOutput:
        """Stop the current audio playback completely."""
        try:
            self.stop_playback.set()
            self.is_playing = False
            self.is_paused = False

            if self.playback_thread and self.playback_thread.is_alive():
                self.playback_thread.join(timeout=2.0)

            self.logger.info(
                "TTS playback stopped",
                action="stop_playback",
                layer="tts_playback_controller"
            )

            return StateOutput(
                status=StatusType.SUCCESS,
                message="TTS playback stopped",
                code=StatusCode.OK,
                outputs={"stopped": True},
                meta={"playback_state": "stopped"}
            )

        except Exception as e:
            self.logger.error(
                "Error stopping TTS playback",
                action="stop_playback",
                reason=str(e),
                layer="tts_playback_controller"
            )

            return StateOutput(
                status=StatusType.ERROR,
                message=f"Failed to stop TTS playback: {str(e)}",
                code=StatusCode.INTERNAL_ERROR,
                outputs={},
                meta={"error": str(e)}
            )

    def get_current_playback_position(self) -> float:
        """Get current playback position in seconds."""
        if not self.playback_start_time:
            return 0.0

        elapsed = time.time() - self.playback_start_time - self.total_pause_duration
        if self.is_paused and self.pause_time:
            elapsed -= (time.time() - self.pause_time)

        return max(0.0, elapsed)

    def get_playback_status(self) -> Dict[str, Any]:
        """Get comprehensive playback status."""
        return {
            "is_playing": self.is_playing,
            "is_paused": self.is_paused,
            "current_position": self.get_current_playback_position(),
            "audio_duration": self.audio_duration,
            "audio_path": self.current_audio_path,
            "interrupt_detected": self.interrupt_detected,
            "progress_percentage": (self.get_current_playback_position() / self.audio_duration * 100) if self.audio_duration else 0
        }

    async def _get_audio_duration(self, audio_path: str) -> float:
        """Get audio file duration in seconds."""
        try:
            # For now, use a simple estimation based on file size
            # In a production system, you'd use a proper audio library like pydub
            file_size = os.path.getsize(audio_path)
            # Rough estimation: MP3 at 128kbps ≈ 16KB/second
            estimated_duration = file_size / 16000
            return max(1.0, estimated_duration)  # Minimum 1 second
        except Exception:
            return 5.0  # Default fallback duration

    def _playback_worker(self, audio_path: str, resume_from_position: float = 0.0):
        """Worker thread for audio playback simulation with partial resume support."""
        try:
            # This is a simplified playback simulation
            # In production, you'd use pygame, pydub, or similar for actual audio playback

            duration = self.audio_duration or 5.0
            chunk_size = 0.1  # 100ms chunks
            total_chunks = int(duration / chunk_size)
            # Calculate starting chunk based on resume_from_position
            start_chunk = int(resume_from_position / chunk_size)
            chunks_played = start_chunk

            while chunks_played < total_chunks and not self.stop_playback.is_set():
                # Check for pause
                if self.pause_playback_event.is_set():
                    while self.pause_playback_event.is_set() and not self.stop_playback.is_set():
                        time.sleep(0.1)
                    continue

                # Simulate playing a chunk
                time.sleep(chunk_size)
                chunks_played += 1

            # Playback completed
            self.is_playing = False
            self.is_paused = False

        except Exception as e:
            self.logger.error(
                "Error in playback worker",
                action="_playback_worker",
                reason=str(e),
                layer="tts_playback_controller"
            )

    async def _interrupt_detection_loop(self):
        """Real-time interrupt detection with PyAudio and VAD processing."""
        if self.interrupt_config and not self.interrupt_config.global_settings.enabled:
            self.logger.info(
                "Interrupt detection is disabled in config.",
                action="_interrupt_detection_loop",
                layer="tts_playback_controller"
            )
            return

        if not _pyaudio_available:
            self.logger.warning(
                "PyAudio not available, falling back to simulated interrupt detection",
                action="_interrupt_detection_loop",
                layer="tts_playback_controller"
            )
            # Fallback to simulated detection
            await asyncio.sleep(1.0)
            if self.is_playing and not self.stop_playback.is_set():
                await self._handle_interrupt()
            return

        speech_start_time = None
        # Get confirmation window from config
        confirmation_window = self.interrupt_config.global_settings.confirmation_window_seconds if self.interrupt_config else 0.5

        try:
            # Audio capture parameters
            sample_rate = 16000
            chunk_size = 1024  # Process 64ms chunks (1024 samples at 16kHz)
            format = pyaudio.paInt16
            channels = 1

            # Initialize PyAudio
            audio = pyaudio.PyAudio()

            # Open microphone stream
            stream = audio.open(
                format=format,
                channels=channels,
                rate=sample_rate,
                input=True,
                frames_per_buffer=chunk_size
            )

            self.logger.info(
                "Started real-time interrupt detection with microphone",
                action="_interrupt_detection_loop",
                output_data={
                    "sample_rate": sample_rate,
                    "chunk_size": chunk_size,
                    "confirmation_window": confirmation_window
                },
                layer="tts_playback_controller"
            )

            while self.is_playing and not self.stop_playback.is_set():
                try:
                    # Capture audio chunk
                    audio_chunk = stream.read(chunk_size, exception_on_overflow=False)

                    # Process with VAD
                    vad_result = detect_voice_activity(audio_chunk, session_id=self.session_id)

                    if vad_result.outputs.get("has_voice", False):
                        if speech_start_time is None:
                            speech_start_time = time.time()
                            self.logger.debug(
                                "Speech detected, starting confirmation timer",
                                action="_interrupt_detection_loop",
                                layer="tts_playback_controller"
                            )
                        elif time.time() - speech_start_time >= confirmation_window:
                            # Confirmed interrupt after 0.5s of continuous speech
                            self.logger.info(
                                "Interrupt confirmed after continuous speech",
                                action="_interrupt_detection_loop",
                                output_data={
                                    "speech_duration": time.time() - speech_start_time,
                                    "confirmation_window": confirmation_window
                                },
                                layer="tts_playback_controller"
                            )
                            await self._handle_interrupt()
                            speech_start_time = None
                            break
                    else:
                        # Reset speech timer if no voice detected
                        if speech_start_time is not None:
                            self.logger.debug(
                                "Speech ended, resetting confirmation timer",
                                action="_interrupt_detection_loop",
                                layer="tts_playback_controller"
                            )
                        speech_start_time = None

                    # Process every 100ms
                    await asyncio.sleep(0.1)

                except Exception as chunk_error:
                    self.logger.warning(
                        f"Error processing audio chunk: {chunk_error}",
                        action="_interrupt_detection_loop",
                        layer="tts_playback_controller"
                    )
                    await asyncio.sleep(0.1)

            # Cleanup
            stream.stop_stream()
            stream.close()
            audio.terminate()

        except Exception as e:
            self.logger.error(
                "Error in real-time interrupt detection loop",
                action="_interrupt_detection_loop",
                reason=str(e),
                layer="tts_playback_controller"
            )

    def _should_check_for_interrupt(self) -> bool:
        """Determine if we should check for interrupts (avoid checking too frequently)."""
        # Only check if we've been playing for at least 0.5 seconds
        return self.get_current_playback_position() > 0.5

    async def _detect_interrupt(self) -> bool:
        """Deprecated: now handled in _interrupt_detection_loop with real VAD."""
        return False

    async def _handle_interrupt(self):
        """Handle detected interrupt with enhanced position tracking."""
        try:
            self.interrupt_detected = True

            # Store current playback position before pausing
            position = self.get_current_playback_position()
            self.interrupted_position = position

            # Pause playback
            await self.pause_playback()

            self.logger.info(
                "Interrupt detected and handled",
                action="_handle_interrupt",
                output_data={
                    "playback_position": position,
                    "audio_duration": self.audio_duration,
                    "interrupted_position_stored": self.interrupted_position
                },
                layer="tts_playback_controller"
            )

            # Call interrupt callback if provided
            if self.interrupt_callback:
                await self.interrupt_callback({
                    "session_id": self.session_id,
                    "playback_position": position,
                    "audio_path": self.current_audio_path,
                    "timestamp": datetime.now().isoformat(),
                    "action": "interrupt"
                })

        except Exception as e:
            self.logger.error(
                "Error handling interrupt",
                action="_handle_interrupt",
                reason=str(e),
                layer="tts_playback_controller"
            )

    async def resume_after_user_finishes(self):
        """Resume TTS playback after user finishes speaking."""
        try:
            # Wait for user to finish speaking (VAD-based detection)
            await self._wait_for_user_speech_end()

            # Resume TTS from stored position
            if hasattr(self, 'interrupted_position') and self.interrupted_position is not None:
                self.logger.info(
                    "Resuming TTS after user finished speaking",
                    action="resume_after_user_finishes",
                    output_data={
                        "resume_position": self.interrupted_position,
                        "audio_path": self.current_audio_path
                    },
                    layer="tts_playback_controller"
                )
                await self.resume_playback_from_position(
                    self.current_audio_path,
                    self.interrupted_position
                )
            else:
                self.logger.warning(
                    "No interrupted position stored, cannot resume TTS",
                    action="resume_after_user_finishes",
                    layer="tts_playback_controller"
                )

        except Exception as e:
            self.logger.error(
                "Error resuming TTS after user finishes",
                action="resume_after_user_finishes",
                reason=str(e),
                layer="tts_playback_controller"
            )

    async def _wait_for_user_speech_end(self):
        """Wait for user to finish speaking using VAD detection."""
        if not _pyaudio_available:
            # Fallback: wait a fixed time
            await asyncio.sleep(2.0)
            return

        try:
            silence_duration = 0.0
            # Get required silence duration from config
            required_silence = self.interrupt_config.global_settings.user_speech_end_silence_seconds if self.interrupt_config else 1.5

            # Audio capture parameters
            sample_rate = 16000
            chunk_size = 1024
            format = pyaudio.paInt16
            channels = 1

            audio = pyaudio.PyAudio()
            stream = audio.open(
                format=format,
                channels=channels,
                rate=sample_rate,
                input=True,
                frames_per_buffer=chunk_size
            )

            self.logger.info(
                "Waiting for user to finish speaking",
                action="_wait_for_user_speech_end",
                output_data={"required_silence": required_silence},
                layer="tts_playback_controller"
            )

            while silence_duration < required_silence:
                try:
                    audio_chunk = stream.read(chunk_size, exception_on_overflow=False)
                    vad_result = detect_voice_activity(audio_chunk, session_id=self.session_id)

                    if vad_result.outputs.get("has_voice", False):
                        # User is still speaking, reset silence timer
                        silence_duration = 0.0
                    else:
                        # Accumulate silence time
                        silence_duration += 0.1  # 100ms chunks

                    await asyncio.sleep(0.1)

                except Exception as chunk_error:
                    self.logger.warning(
                        f"Error processing audio chunk while waiting: {chunk_error}",
                        action="_wait_for_user_speech_end",
                        layer="tts_playback_controller"
                    )
                    await asyncio.sleep(0.1)

            # Cleanup
            stream.stop_stream()
            stream.close()
            audio.terminate()

            self.logger.info(
                "User finished speaking, ready to resume TTS",
                action="_wait_for_user_speech_end",
                output_data={"silence_duration": silence_duration},
                layer="tts_playback_controller"
            )

        except Exception as e:
            self.logger.error(
                "Error waiting for user speech end",
                action="_wait_for_user_speech_end",
                reason=str(e),
                layer="tts_playback_controller"
            )


# Convenience functions for easy access
def analyze_audio(audio_data: bytes, session_id: str = None) -> StateOutput:
    """Convenience function for audio analysis."""
    processor = AudioProcessor(interrupt_config=interrupt_config)
    return processor.analyze_audio_properties(audio_data, session_id)


def detect_voice_activity(audio_data: bytes, threshold: float = None, session_id: str = None) -> StateOutput:
    """Convenience function for voice activity detection."""
    processor = AudioProcessor(interrupt_config=interrupt_config)
    return processor.detect_voice_activity(audio_data, threshold, session_id)


def validate_audio_quality(audio_data: bytes, session_id: str = None) -> StateOutput:
    """Convenience function for audio quality validation."""
    processor = AudioProcessor(interrupt_config=interrupt_config)
    return processor.validate_audio_quality(audio_data, session_id)



async def record_microphone_audio_vad(sample_rate=16000, device_index=None, silence_duration=1.5, max_recording=15):
    """Record audio from microphone, stop when user stops speaking (VAD endpointing)."""
    vad = webrtcvad.Vad(3)  # Most aggressive
    chunk_duration = 0.03  # 30ms
    chunk_size = int(sample_rate * chunk_duration)
    silence_chunks = int(silence_duration / chunk_duration)
    max_chunks = int(max_recording / chunk_duration)

    print("🎤 [MIC] Recording... Speak now! (Recording will stop when you are silent)")

    audio_buffer = []
    silence_counter = 0

    with sd.InputStream(samplerate=sample_rate, channels=1, dtype='int16', device=device_index, blocksize=chunk_size) as stream:
        for i in range(max_chunks):
            chunk, _ = stream.read(chunk_size)
            chunk_bytes = chunk.tobytes()
            audio_buffer.append(chunk_bytes)

            # VAD expects 16-bit mono PCM
            is_speech = vad.is_speech(chunk_bytes, sample_rate)
            if is_speech:
                silence_counter = 0
            else:
                silence_counter += 1

            if silence_counter > silence_chunks:
                print("🛑 Silence detected, stopping recording.")
                break

    # Save audio to file
    timestamp = int(time.time())
    audio_filename = f'recorded_audio_{timestamp}.wav'
    audio_data = b''.join(audio_buffer)
    with wave.open(audio_filename, 'wb') as wf:
        wf.setnchannels(1)
        wf.setsampwidth(2)
        wf.setframerate(sample_rate)
        wf.writeframes(audio_data)

    print(f"✅ [INFO] Saved recorded audio to: {audio_filename}")
    return audio_filename
# def synthesize_fallback_audio(text, session_id="fallback"):
#     try:
#         api_key = os.getenv("ELEVENLABS_API_KEY")
#         client = ElevenLabs(api_key=api_key)
#         voice_id = "EXAVITQu4vr4xnSDxMaL"  # Default voice
#         audio_path = os.path.abspath(f"tts_fallback_{session_id}.mp3")
#         audio_gen = client.text_to_speech.convert(
#             text=text,
#             voice_id=voice_id,
#             model_id="eleven_multilingual_v2",
#             output_format="mp3_44100_128"
#         )
#         with open(audio_path, "wb") as f:
#             for chunk in audio_gen:
#                 f.write(chunk)
#         return audio_path
#     except Exception as e:
#         print(f"[Fallback TTS] Could not generate fallback audio: {e}")
#         return None


#fallback using google TTS PROVIDER
def synthesize_fallback_audio(text, session_id="fallback"):
    try:
        from google.cloud import texttospeech
        import os

        # Create client
        client = texttospeech.TextToSpeechClient()

        # Set input text
        synthesis_input = texttospeech.SynthesisInput(text=text)

        # Use default fallback voice (female, en-US)
        voice = texttospeech.VoiceSelectionParams(
            language_code="en-US",
            ssml_gender=texttospeech.SsmlVoiceGender.FEMALE
        )

        # Select audio format
        audio_config = texttospeech.AudioConfig(
            audio_encoding=texttospeech.AudioEncoding.MP3
        )

        # Generate speech
        response = client.synthesize_speech(
            input=synthesis_input, voice=voice, audio_config=audio_config
        )

        # Save to file
        project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..'))
        audio_path = os.path.join(project_root, f"tts_fallback_{session_id}.mp3")

        with open(audio_path, "wb") as out:
            out.write(response.audio_content)

        return audio_path
    except Exception as e:
        print(f"[Fallback TTS] Could not generate fallback audio: {e}")
        return None
